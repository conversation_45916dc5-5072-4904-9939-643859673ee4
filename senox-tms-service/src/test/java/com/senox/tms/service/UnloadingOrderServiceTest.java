package com.senox.tms.service;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.UnloadingBaseTest;
import com.senox.tms.constant.UnloadingOrderState;
import com.senox.tms.domain.*;
import com.senox.tms.vo.UnloadingOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 8:42
 */
@Slf4j
public class UnloadingOrderServiceTest extends UnloadingBaseTest {

    @Autowired
    private UnloadingWorkerService workerService;

    @Autowired
    private UnloadingDictService dictService;

    @Autowired
    private UnloadingOrderService orderService;


    @BeforeEach
    void setUp() {
        List<UnloadingDict> dictList = dictMock(5);
        log.info("dictList Mock ---- : {}", JsonUtils.object2Json(dictList));
        dictService.batchAddDict(dictList);
        List<UnloadingWorker> workers = workerMock(5);
        workers.forEach(worker -> workerService.addWorker(worker));
    }

    @Test
    void assignOrder(){
        UnloadingOrder order = mockOrder();
        List<UnloadingOrderGoods> goods = mockGoods(3);
        orderService.saveOrder(order, goods);
        UnloadingOrder dbOrder = orderService.findById(1L);
        log.info("dbOrder ---- : {}", JsonUtils.object2Json(dbOrder));
        Assertions.assertNotNull(dbOrder);
        UnloadingOrderVo dbOrderDetail = orderService.findDetailById(1L);
        log.info("dbOrderDetail ---- : {}", JsonUtils.object2Json(dbOrderDetail));
        Assertions.assertNotNull(dbOrderDetail);

        orderService.appointWorkerNum(1L, 3);
        UnloadingOrder appointNumOrder = orderService.findById(1L);
        log.info("appointNumOrder ---- : {}", JsonUtils.object2Json(appointNumOrder));
        Assertions.assertNotNull(appointNumOrder);

        List<UnloadingOrderWorkers> workersList = mockWorkers(3);
        orderService.assignWorkers(1L, workersList);
        UnloadingOrderVo assignDbOrderDetail = orderService.findDetailById(1L);
        log.info("assignDbOrderDetail ---- : {}", JsonUtils.object2Json(assignDbOrderDetail));
        Assertions.assertNotNull(assignDbOrderDetail);
    }

    private static List<UnloadingOrderWorkers> mockWorkers(int count) {
        List<UnloadingOrderWorkers> workersList = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            UnloadingOrderWorkers workers = new UnloadingOrderWorkers();
            workers.setWorkerId((long)(i+1));
            workers.setWorkerNo(randStr(5).concat("----" + i));
            workersList.add(workers);
        }
        return workersList;
    }

    private static List<UnloadingOrderGoods> mockGoods(int count) {
        List<UnloadingOrderGoods> goods = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            UnloadingOrderGoods orderGoods = new UnloadingOrderGoods();
            orderGoods.setGoodsName(randStr(5).concat("---" + i));
            orderGoods.setCategory(randInt(1, 5));
            orderGoods.setUnit("斤");
            orderGoods.setQuantity(randDecimal(new BigDecimal("21"), new BigDecimal("30"), 2));
            goods.add(orderGoods);
        }
        return goods;
    }

    private static UnloadingOrder mockOrder() {
        UnloadingOrder order = new UnloadingOrder();
        order.setLocation(randStr(5).concat("--location"));
        order.setLicensePlate(randStr(5).concat("--licensePlate"));
        order.setContact(randNumStr(11));
        order.setRemark(randStr(10).concat("--remark"));
        order.setState(UnloadingOrderState.NORMAL.getNumber());
        order.setCarNum(5);
        order.setOpenid(randStr(15));
        order.setCarCategory(9);
        order.setCarCategoryName("叉车");
        return order;
    }
}
