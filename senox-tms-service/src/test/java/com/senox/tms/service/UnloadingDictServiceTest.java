package com.senox.tms.service;

import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.UnloadingBaseTest;
import com.senox.tms.domain.UnloadingDict;
import com.senox.tms.vo.UnloadingDictSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 8:27
 */
@Slf4j
public class UnloadingDictServiceTest extends UnloadingBaseTest {

    @Autowired
    private UnloadingDictService dictService;

    @Test
    void addDict() {
        List<UnloadingDict> dictList = dictMock(5);
        log.info("dictList Mock ---- : {}", JsonUtils.object2Json(dictList));
        dictService.batchAddDict(dictList);
        UnloadingDictSearchVo searchVo = new UnloadingDictSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        PageResult<UnloadingDict> pageResult = dictService.dictPageResult(searchVo);
        log.info("pageResult ---- : {}", JsonUtils.object2Json(pageResult));
        Assertions.assertNotNull(pageResult);
    }

    @Test
    void updateDict() {
        List<UnloadingDict> dictList = dictMock(5);
        log.info("dictList Mock ---- : {}", JsonUtils.object2Json(dictList));
        dictService.batchAddDict(dictList);
        UnloadingDict dbDict = dictService.findById(1L);
        log.info("dbDict ---- : {}", JsonUtils.object2Json(dbDict));
        dbDict.setName(dbDict.getName().concat("-update"));
        dbDict.setUnit("个");
        dictService.updateDict(dbDict);
        UnloadingDict updateDict = dictService.findById(dbDict.getId());
        log.info("updateDict ---- : {}", JsonUtils.object2Json(updateDict));
        Assertions.assertNotEquals(dbDict, updateDict);
    }

    @Test
    void deleteDict() {
        List<UnloadingDict> dictList = dictMock(5);
        log.info("dictList Mock ---- : {}", JsonUtils.object2Json(dictList));
        dictService.batchAddDict(dictList);
        UnloadingDict dbDict = dictService.findById(1L);
        log.info("dbDict ---- : {}", JsonUtils.object2Json(dbDict));
        dictService.deleteDict(dbDict);
        UnloadingDict deleteDict = dictService.findById(dbDict.getId());
        log.info("deleteDict ---- : {}", JsonUtils.object2Json(deleteDict));
        Assertions.assertNotEquals(dbDict, deleteDict);
        UnloadingDictSearchVo searchVo = new UnloadingDictSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(5);
        PageResult<UnloadingDict> pageResult = dictService.dictPageResult(searchVo);
        log.info("pageResult ---- : {}", JsonUtils.object2Json(pageResult));
    }
}
