package com.senox.tms.service;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.UnloadingBaseTest;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.domain.UnloadingAttendance;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.vo.UnloadingWorkerSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 14:39
 */
@Slf4j
public class UnloadingWorkerServiceTest extends UnloadingBaseTest {

    @Autowired
    private UnloadingWorkerService workerService;

    @Autowired
    private UnloadingAttendanceService attendanceService;

    @Test
    void addWorker() {
        List<UnloadingWorker> workers = workerMock(5);
        workers.forEach(worker -> workerService.addWorker(worker));
        UnloadingWorkerSearchVo searchVo = new UnloadingWorkerSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        List<UnloadingWorker> workerList = workerService.listWorker(searchVo);
        log.info("workerList ---- : {}", JsonUtils.object2Json(workerList));
        Assertions.assertNotNull(workerList);
    }

    @Test
    void updateWorker() {
        List<UnloadingWorker> workers = workerMock(5);
        workers.forEach(worker -> workerService.addWorker(worker));
        UnloadingWorker dbWorker = workerService.findById(1L);
        log.info("dbWorker ---- : {}", JsonUtils.object2Json(dbWorker));
        workerService.updateWorkerStatus(dbWorker.getId(), UnloadingWorkerStatus.ALREADY_LISTED.getNumber());
        UnloadingWorker updateWorker = workerService.findById(1L);
        log.info("updateWorker ---- : {}", JsonUtils.object2Json(updateWorker));
        Assertions.assertNotEquals(dbWorker, updateWorker);
        UnloadingAttendance dbAttendance = attendanceService.findById(1L);
        log.info("dbAttendance ---- : {}", JsonUtils.object2Json(dbAttendance));
        attendanceService.updateRemark(dbAttendance.getId(), "测试改备注");
        UnloadingAttendance updateAttendance = attendanceService.findById(1L);
        log.info("updateAttendance ---- : {}", JsonUtils.object2Json(updateAttendance));
        Assertions.assertNotEquals(dbAttendance, updateAttendance);
    }

    @Test
    void testLogicalDeleteWorker() {
        // 1. 创建测试搬运工
        List<UnloadingWorker> workers = workerMock(3);
        workers.forEach(worker -> workerService.addWorker(worker));

        // 2. 验证搬运工存在
        UnloadingWorkerSearchVo searchVo = new UnloadingWorkerSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        List<UnloadingWorker> workerListBefore = workerService.listWorker(searchVo);
        log.info("删除前搬运工列表数量: {}", workerListBefore.size());
        Assertions.assertEquals(3, workerListBefore.size());

        // 3. 获取要删除的搬运工
        UnloadingWorker workerToDelete = workerListBefore.get(0);
        Long workerId = workerToDelete.getId();
        log.info("准备删除搬运工: {}", JsonUtils.object2Json(workerToDelete));

        // 4. 执行逻辑删除
        workerService.deleteWorker(workerId);
        log.info("已执行逻辑删除操作");

        // 5. 验证搬运工在列表中不再显示（被逻辑删除）
        List<UnloadingWorker> workerListAfter = workerService.listWorker(searchVo);
        log.info("删除后搬运工列表数量: {}", workerListAfter.size());
        Assertions.assertEquals(2, workerListAfter.size());

        // 6. 验证搬运工数据仍然存在于数据库中，但被标记为禁用
        UnloadingWorker deletedWorker = workerService.getById(workerId);
        log.info("被删除的搬运工数据: {}", JsonUtils.object2Json(deletedWorker));
        Assertions.assertNotNull(deletedWorker);
        Assertions.assertTrue(deletedWorker.getDisabled());

        log.info("逻辑删除测试通过！");
    }
}
