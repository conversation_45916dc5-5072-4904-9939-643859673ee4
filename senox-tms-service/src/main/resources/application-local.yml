spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************
    username: root
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 10000
  rabbitmq:
    addresses: localhost:5672
    username: guest
    password: guest
    virtual-host: /
    connection-timeout: 15000
    consumer-threads: 5
  zipkin:
    enabled: false

# 禁用XXL-Job
xxl:
  job:
    enabled: false

logging:
  level:
    com.senox.tms.mapper: debug
