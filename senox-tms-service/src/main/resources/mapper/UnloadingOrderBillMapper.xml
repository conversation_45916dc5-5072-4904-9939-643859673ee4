<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingOrderBillMapper">

    <select id="countBill" parameterType="com.senox.tms.vo.UnloadingOrderBillSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_order_bill
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="billDateStart != null">
                AND bill_date >= #{billDateStart}
            </if>
            <if test="billDateEnd != null">
                AND bill_date <![CDATA[<=]]> #{billDateEnd}
            </if>
            <if test="paidTimeStart != null">
                AND paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listBill" parameterType="com.senox.tms.vo.UnloadingOrderBillSearchVo" resultType="com.senox.tms.domain.UnloadingOrderBill">
        SELECT
              id
            , bill_date
            , bill_year
            , bill_month
            , order_no
            , amount
            , status
            , paid_time
        FROM px_unloading_order_bill
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="billDateStart != null">
                AND bill_date >= #{billDateStart}
            </if>
            <if test="billDateEnd != null">
                AND bill_date <![CDATA[<=]]> #{billDateEnd}
            </if>
            <if test="paidTimeStart != null">
                AND paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumBill" parameterType="com.senox.tms.vo.UnloadingOrderBillSearchVo" resultType="com.senox.tms.domain.UnloadingOrderBill">
        SELECT
            SUM(amount) as amount
        FROM px_unloading_order_bill
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="billDateStart != null">
                AND bill_date >= #{billDateStart}
            </if>
            <if test="billDateEnd != null">
                AND bill_date <![CDATA[<=]]> #{billDateEnd}
            </if>
            <if test="paidTimeStart != null">
                AND paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
    </select>

</mapper>
