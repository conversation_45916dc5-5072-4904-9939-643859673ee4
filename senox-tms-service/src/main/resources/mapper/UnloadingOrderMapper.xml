<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingOrderMapper">

    <resultMap id="Order_Result" type="com.senox.tms.vo.UnloadingOrderVo">
        <result property="id" column="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="location" column="location"/>
        <result property="licensePlate" column="license_plate"/>
        <result property="contact" column="contact"/>
        <result property="remark" column="remark"/>
        <result property="state" column="state"/>
        <result property="workerStatus" column="worker_status"/>
        <result property="carCategory" column="car_category"/>
        <result property="carCategoryName" column="car_category_name"/>
        <result property="carNum" column="car_num"/>
        <result property="workerNum" column="worker_num"/>
        <result property="orderTime" column="order_time"/>
        <result property="workTime" column="work_time"/>
        <result property="finishTime" column="finish_time"/>
        <result property="amount" column="amount"/>
        <result property="shareAmount" column="share_amount"/>
        <result property="payoffAmount" column="payoff_amount"/>
        <result property="billStatus" column="bill_status"/>
        <result property="payoffStatus" column="payoff_status"/>
        <collection property="goodsVoList" ofType="com.senox.tms.vo.UnloadingOrderGoodsVo" resultMap="OrderGoods_Result"/>
        <collection property="workersVoList" ofType="com.senox.tms.vo.UnloadingOrderWorkersVo" resultMap="OrderWorkers_Result"/>
    </resultMap>

    <resultMap id="OrderGoods_Result" type="com.senox.tms.vo.UnloadingOrderGoodsVo">
        <result property="id" column="goods_id"/>
        <result property="orderId" column="order_id"/>
        <result property="category" column="goods_category"/>
        <result property="goodsName" column="goods_name"/>
        <result property="unit" column="unit"/>
        <result property="quantity" column="quantity"/>
    </resultMap>

    <resultMap id="OrderWorkers_Result" type="com.senox.tms.vo.UnloadingOrderWorkersVo">
        <result property="id" column="workers_id"/>
        <result property="orderId" column="order_id"/>
        <result property="workerId" column="worker_id"/>
        <result property="workerNo" column="worker_no"/>
        <result property="workerName" column="worker_name"/>
    </resultMap>

    <!-- 获取最大的订单号 -->
    <select id="findMaxOrderNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(order_no) FROM px_unloading_order WHERE order_no LIKE CONCAT(#{prefix}, '%');
    </select>

    <select id="countOrder" parameterType="com.senox.tms.vo.UnloadOrderSearchVo" resultType="int">
        SELECT COUNT(*)
        FROM px_unloading_order uo
        LEFT JOIN px_unloading_order_bill uob on uob.order_no = uo.order_no
        LEFT JOIN (
            SELECT order_no
                , MIN(`status`) as status
            from px_unloading_order_payoff
            GROUP BY order_no
        ) as uof on uof.order_no = uo.order_no
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND uo.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and uo.order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="openid != null and openid != ''">
                AND uo.openid = #{openid}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (uo.location LIKE CONCAT('%', #{keywords}, '%') OR uo.license_plate LIKE CONCAT('%', #{keywords}, '%') OR uo.contact LIKE CONCAT('%', #{keywords}, '%'))
            </if>
            <if test="state != null">
                AND uo.`state` = #{state}
            </if>
            <if test="workerStatus != null">
                and worker_status = #{workerStatus}
            </if>
            <if test="workerStatusList != null and workerStatusList.size() > 0">
                and uo.worker_status in
                <foreach collection="workerStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="orderTimeStart != null">
                AND uo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND uo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="workTimeStart != null">
                AND uo.work_time >= #{workTimeStart}
            </if>
            <if test="workTimeEnd != null">
                AND uo.work_time <![CDATA[<=]]> #{workTimeEnd}
            </if>
            <if test="finishTimeStart != null">
                AND uo.finish_time >= #{finishTimeStart}
            </if>
            <if test="finishTimeEnd != null">
                AND uo.finish_time <![CDATA[<=]]> #{finishTimeEnd}
            </if>
            <if test="bill != null">
                <choose>
                    <when test="bill">
                        and uob.status is not null
                    </when>
                    <otherwise>
                        and uob.status is null
                    </otherwise>
                </choose>
            </if>
            <if test="payoff != null">
                <choose>
                    <when test="payoff">
                        and uof.status is not null
                    </when>
                    <otherwise>
                        and uof.status is null
                    </otherwise>
                </choose>
            </if>
            AND uo.is_disabled = 0
        </where>
    </select>

    <select id="listOrder" parameterType="com.senox.tms.vo.UnloadOrderSearchVo" resultType="com.senox.tms.vo.UnloadingOrderVo">
        SELECT uo.id
             , uo.order_no
             , uo.location
             , uo.license_plate
             , uo.contact
             , uo.remark
             , uo.state
             , uo.worker_status
             , uo.car_category
             , uo.car_category_name
             , uo.car_num
             , uo.worker_num
             , uo.order_time
             , uo.work_time
             , uo.finish_time
             , uo.amount
             , uo.amount - uo.payoff_amount as share_amount
             , uo.payoff_amount
             , uob.status as bill_status
             , uof.status as payoff_status
        FROM px_unloading_order uo
        LEFT JOIN px_unloading_order_bill uob on uob.order_no = uo.order_no
        LEFT JOIN (
            SELECT order_no
                 , MIN(`status`) as status
            from px_unloading_order_payoff
            GROUP BY order_no
        ) as uof on uof.order_no = uo.order_no
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND uo.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and uo.order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="openid != null and openid != ''">
                AND uo.openid = #{openid}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (uo.location LIKE CONCAT('%', #{keywords}, '%') OR uo.license_plate LIKE CONCAT('%', #{keywords}, '%') OR uo.contact LIKE CONCAT('%', #{keywords}, '%'))
            </if>
            <if test="state != null">
                AND uo.`state` = #{state}
            </if>
            <if test="workerStatus != null">
                and worker_status = #{workerStatus}
            </if>
            <if test="workerStatusList != null and workerStatusList.size() > 0">
                and uo.worker_status in
                <foreach collection="workerStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="orderTimeStart != null">
                AND uo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND uo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="workTimeStart != null">
                AND uo.work_time >= #{workTimeStart}
            </if>
            <if test="workTimeEnd != null">
                AND uo.work_time <![CDATA[<=]]> #{workTimeEnd}
            </if>
            <if test="finishTimeStart != null">
                AND uo.finish_time >= #{finishTimeStart}
            </if>
            <if test="finishTimeEnd != null">
                AND uo.finish_time <![CDATA[<=]]> #{finishTimeEnd}
            </if>
            <if test="bill != null">
                <choose>
                    <when test="bill">
                        and uob.status is not null
                    </when>
                    <otherwise>
                        and uob.status is null
                    </otherwise>
                </choose>
            </if>
            <if test="payoff != null">
                <choose>
                    <when test="payoff">
                        and uof.status is not null
                    </when>
                    <otherwise>
                        and uof.status is null
                    </otherwise>
                </choose>
            </if>
            AND uo.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY uo.id desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumOrder" parameterType="com.senox.tms.vo.UnloadOrderSearchVo" resultType="com.senox.tms.vo.UnloadingOrderVo">
        SELECT IFNULL(SUM(uo.amount), 0) as amount
             , IFNULL(SUM(uo.amount - uo.payoff_amount), 0) as share_amount
             , IFNULL(SUM(uo.payoff_amount), 0) as payoff_amount
        FROM px_unloading_order uo
                 LEFT JOIN px_unloading_order_bill uob on uob.order_no = uo.order_no
                 LEFT JOIN (
            SELECT order_no
                 , MIN(`status`) as status
            from px_unloading_order_payoff
            GROUP BY order_no
        ) as uof on uof.order_no = uo.order_no
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND uo.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and uo.order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="openid != null and openid != ''">
                AND uo.openid = #{openid}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (uo.location LIKE CONCAT('%', #{keywords}, '%') OR uo.license_plate LIKE CONCAT('%', #{keywords}, '%') OR uo.contact LIKE CONCAT('%', #{keywords}, '%'))
            </if>
            <if test="state != null">
                AND uo.`state` = #{state}
            </if>
            <if test="workerStatus != null">
                and worker_status = #{workerStatus}
            </if>
            <if test="workerStatusList != null and workerStatusList.size() > 0">
                and uo.worker_status in
                <foreach collection="workerStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="orderTimeStart != null">
                AND uo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND uo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="workTimeStart != null">
                AND uo.work_time >= #{workTimeStart}
            </if>
            <if test="workTimeEnd != null">
                AND uo.work_time <![CDATA[<=]]> #{workTimeEnd}
            </if>
            <if test="finishTimeStart != null">
                AND uo.finish_time >= #{finishTimeStart}
            </if>
            <if test="finishTimeEnd != null">
                AND uo.finish_time <![CDATA[<=]]> #{finishTimeEnd}
            </if>
            <if test="bill != null">
                <choose>
                    <when test="bill">
                        and uob.status is not null
                    </when>
                    <otherwise>
                        and uob.status is null
                    </otherwise>
                </choose>
            </if>
            <if test="payoff != null">
                <choose>
                    <when test="payoff">
                        and uof.status is not null
                    </when>
                    <otherwise>
                        and uof.status is null
                    </otherwise>
                </choose>
            </if>
            AND uo.is_disabled = 0
        </where>
    </select>

    <select id="findDetailById" parameterType="java.lang.Long" resultMap="Order_Result">
        SELECT uo.id
             , uo.order_no
             , uo.location
             , uo.license_plate
             , uo.contact
             , uo.remark
             , uo.state
             , uo.worker_status
             , uo.car_category
             , uo.car_category_name
             , uo.car_num
             , uo.worker_num
             , uo.order_time
             , uo.work_time
             , uo.finish_time
             , uo.amount
             , uo.amount - uo.payoff_amount as share_amount
             , uo.payoff_amount
             , uog.id as goods_id
             , uog.category as goods_category
             , uog.goods_name
             , uog.unit
             , uog.quantity
             , uow.id as workers_id
             , uow.worker_id
             , uow.worker_no
             , uow.worker_name
             , uo.id as order_id
             , uob.status as bill_status
        FROM px_unloading_order uo
        LEFT JOIN px_unloading_order_goods uog ON uo.id = uog.order_id
        LEFT JOIN px_unloading_order_workers uow ON uo.id = uow.order_id
        LEFT JOIN px_unloading_order_bill uob on uob.order_no = uo.order_no
        WHERE uo.id = #{id}
    </select>
</mapper>
