<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticLoaderSettlementMapper">

    <resultMap id="loaderSettlementResultMap" type="com.senox.tms.vo.LogisticLoaderSettlementVo">
        <id property="id" column="id"/>
        <result property="date" column="date" />
        <result property="freightType" column="freight_type" typeHandler="com.senox.tms.handler.GenericEnumTypeHandler"/>
        <result property="customerId" column="customer_id" />
        <result property="customerName" column="customer_name" />
        <result property="goodsType" column="goods_type" typeHandler="com.senox.tms.handler.GenericEnumTypeHandler"/>
        <result property="transportAvg" column="transport_avg" />
        <result property="transportTotal" column="transport_total" />
        <result property="participationNumber" column="participation_number" />
        <result property="freightUnitPrice" column="freight_unit_price" />
        <result property="freightTotalAmount" column="freight_total_amount" />
        <result property="sortingFee" column="sorting_fee" />
        <result property="pieces" column="pieces" />
        <result property="carNo" column="car_no" />
        <result property="remark" column="remark"/>
        <result property="drivingHours" column="driving_hours" />
        <result property="mealAllowanceAmount" column="meal_allowance_amount" />
        <result property="subtotalAmount" column="subtotal_amount" />
        <result property="totalAmount" column="total_amount" />
        <result property="creatorName" column="creator_name" />
        <result property="createTime" column="create_time" />
        <collection property="loaders" select="incomeListBySettlement" ofType="com.senox.tms.vo.DictLogisticVo" column="{settlementId=id}" >
        </collection>
    </resultMap>

    <insert id="addBatch" useGeneratedKeys="true" keyProperty="id">
        insert into t_logistic_loader_settlement(`date`, freight_type, customer_id, goods_type, transport_avg,
        transport_total, participation_number, freight_unit_price, freight_total_amount, sorting_fee, pieces, car_no, remark,
        driving_hours, meal_allowance_amount, subtotal_amount, total_amount, creator_id, creator_name,
        create_time, modifier_id, modifier_name, modified_time)
        values
        <foreach collection="settlements" item="item" separator=",">
            (#{item.date},#{item.freightType},#{item.customerId}
            ,#{item.goodsType},#{item.transportAvg},#{item.transportTotal}
            ,#{item.participationNumber},#{item.freightUnitPrice},#{item.freightTotalAmount}
            ,#{item.sortingFee}, #{item.pieces}, #{item.carNo}, #{item.remark}, #{item.drivingHours},#{item.mealAllowanceAmount}
            ,#{item.subtotalAmount},#{item.totalAmount},#{item.creatorId},#{item.creatorName}
            ,now() ,#{item.modifierId},#{item.modifierName},now())
        </foreach>
    </insert>

    <update id="updateBatch">
        update t_logistic_loader_settlement
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`date` = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.date">
                        when id = #{item.id} then #{item.date}
                    </if>
                </foreach>
            </trim>
            <trim prefix="customer_id = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.customerId">
                        when id = #{item.id} then #{item.customerId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="freight_type = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.freightType">
                        when id = #{item.id} then #{item.freightType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="goods_type = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.goodsType">
                        when id = #{item.id} then #{item.goodsType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="transport_avg = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.transportAvg">
                        when id = #{item.id} then #{item.transportAvg}
                    </if>
                </foreach>
            </trim>
            <trim prefix="transport_total = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.transportTotal">
                        when id = #{item.id} then #{item.transportTotal}
                    </if>
                </foreach>
            </trim>
            <trim prefix="participation_number = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.participationNumber">
                        when id = #{item.id} then #{item.participationNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix="freight_unit_price = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.freightUnitPrice">
                        when id = #{item.id} then #{item.freightUnitPrice}
                    </if>
                </foreach>
            </trim>
            <trim prefix="freight_total_amount = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.freightTotalAmount">
                        when id = #{item.id} then #{item.freightTotalAmount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sorting_fee = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.sortingFee">
                        when id = #{item.id} then #{item.sortingFee}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pieces = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.pieces">
                        when id = #{item.id} then #{item.pieces}
                    </if>
                </foreach>
            </trim>
            <trim prefix="car_no = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.carNo">
                        when id = #{item.id} then #{item.carNo}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.remark">
                        when id = #{item.id} then #{item.remark}
                    </if>
                </foreach>
            </trim>
            <trim prefix="driving_hours = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.drivingHours">
                        when id = #{item.id} then #{item.drivingHours}
                    </if>
                </foreach>
            </trim>
            <trim prefix="meal_allowance_amount = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.mealAllowanceAmount">
                        when id = #{item.id} then #{item.mealAllowanceAmount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="subtotal_amount = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.subtotalAmount">
                        when id = #{item.id} then #{item.subtotalAmount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="total_amount = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.totalAmount">
                        when id = #{item.id} then #{item.totalAmount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.modifierId">
                        when id = #{item.id} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = case" suffix="end,">
                <foreach collection="settlements" item="item">
                    <if test="null != item.modifierName">
                        when id = #{item.id} then #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = now()
        </trim>
        <where>
            and id in
            <foreach collection="settlements" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </where>
    </update>

    <select id="getById" resultMap="loaderSettlementResultMap">
        select ls.id,
        ls.`date`,
        ls.freight_type,
        dl1.id           as customer_id,
        dl1.name         as customer_name,
        ls.goods_type,
        ls.transport_avg,
        ls.transport_total,
        ls.participation_number,
        ls.freight_unit_price,
        ls.freight_total_amount,
        ls.sorting_fee,
        ls.pieces,
        ls.car_no,
        ls.remark,
        ls.driving_hours,
        ls.meal_allowance_amount,
        ls.subtotal_amount,
        ls.total_amount,
        ls.creator_id,
        au.real_name     as creator_name,
        ls.create_time,
        ls.modifier_id,
        ls.modifier_name,
        ls.modified_time
        from t_logistic_loader_settlement ls
        inner join t_dict_logistic dl1 on dl1.id = ls.customer_id
        inner join u_admin_user au on au.id = ls.creator_id
        <where>
            and ls.id = #{id}
            and ls.is_disabled = false
        </where>
    </select>

    <select id="list" resultMap="loaderSettlementResultMap">
        select ls.id,
        ls.`date`,
        ls.freight_type,
        dl1.id           as customer_id,
        dl1.name         as customer_name,
        ls.goods_type,
        ls.transport_avg,
        ls.transport_total,
        ls.participation_number,
        ls.freight_unit_price,
        ls.freight_total_amount,
        ls.sorting_fee,
        ls.pieces,
        ls.car_no,
        ls.remark,
        ls.driving_hours,
        ls.meal_allowance_amount,
        ls.subtotal_amount,
        ls.total_amount,
        ls.creator_id,
        au.real_name     as creator_name,
        ls.create_time,
        ls.modifier_id,
        ls.modifier_name,
        ls.modified_time
        from t_logistic_loader_settlement ls
        inner join t_dict_logistic dl1 on dl1.id = ls.customer_id
        inner join u_admin_user au on au.id = ls.creator_id
        <where>
            <if test="null != startDate">
                and ls.`date` >= #{startDate}
            </if>
            <if test="null != endDate">
                and ls.`date` &lt;= #{endDate}
            </if>
            <if test="null != customerName and customerName != ''">
                and dl1.name like concat('%',#{customerName},'%')
            </if>
            <if test="null != carNo and carNo != ''">
                and ls.car_no like concat('%',#{carNo},'%')
            </if>
            <if test="null != freightType">
                and ls.freight_type = #{freightType.number}
            </if>
            <if test="null != goodsType">
                and ls.goods_type = #{goodsType.number}
            </if>
            and ls.is_disabled = false
        </where>
        order by ls.date desc, ls.create_time desc
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="countList" resultType="int">
        select count(ls.id)
        from t_logistic_loader_settlement ls
        inner join t_dict_logistic dl on dl.id = ls.customer_id
        <where>
            <if test="null != startDate">
                and ls.`date` >= #{startDate}
            </if>
            <if test="null != endDate">
                and ls.`date` &lt;= #{endDate}
            </if>
            <if test="null != customerName and customerName != ''">
                and dl.name like concat('%',#{customerName},'%')
            </if>
            <if test="null != carNo and carNo != ''">
                and ls.car_no like concat('%',#{carNo},'%')
            </if>
            <if test="null != freightType">
                and ls.freight_type = #{freightType.number}
            </if>
            <if test="null != goodsType">
                and ls.goods_type = #{goodsType.number}
            </if>
            and ls.is_disabled = false
        </where>
    </select>

    <select id="listTotalAmount" resultType="BigDecimal">
        select sum(ls.total_amount)
        from t_logistic_loader_settlement ls
        <where>
            <if test="null != startDate">
                and ls.`date` >= #{startDate}
            </if>
            <if test="null != endDate">
                and ls.`date` &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="incomeListBySettlement" resultType="com.senox.tms.vo.DictLogisticVo">
        select dl.id,loader_number as `key`, loader_name as name
        from t_logistic_loader_income li
        inner join t_dict_logistic dl on dl.`key` = li.loader_number and category = 1
        <where>
            <if test="null != settlementId and settlementId > 0">
                and settlement_id = #{settlementId}
            </if>
            and li.is_disabled = false
        </where>
    </select>
</mapper>
