<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleDeliveryOrderDetailMapper">

    <resultMap id="DeliveryOrderDetailVo_Result" type="com.senox.tms.vo.BicycleDeliveryOrderDetailVo">
        <result property="id" column="id"/>
        <result property="deliveryOrderSerialNo" column="delivery_order_serial_no"/>
        <result property="deliveryOrderSerialNoItem" column="delivery_order_serial_no_item"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="chargesId" column="charges_id"/>
        <result property="status" column="status"/>
        <result property="pickingPoint" column="picking_point"/>
        <result property="pickedPieces" column="picked_pieces"/>
        <result property="deliveryPoint" column="delivery_point"/>
        <result property="pickingTime" column="picking_time"/>
        <result property="sendTime" column="send_time"/>
        <result property="receivingTime" column="receiving_time"/>
        <result property="finishTime" column="finish_time"/>
        <result property="rating" column="rating"/>
        <result property="startPointId" column="start_point_id"/>
        <result property="startPointName" column="start_point_name"/>
        <result property="startPointDetailName" column="start_point_detail_name"/>
        <result property="endPointId" column="end_point_id"/>
        <result property="endPointName" column="end_point_name"/>
        <result property="endPointDetailName" column="end_point_detail_name"/>
        <result property="sendTimeStart" column="send_time_start"/>
        <result property="sendTimeEnd" column="send_time_end"/>
        <result property="senderId" column="sender_id"/>
        <result property="sender" column="sender"/>
        <result property="senderSerialNo" column="sender_serial_no"/>
        <result property="senderContact" column="sender_contact"/>
        <result property="recipient" column="recipient"/>
        <result property="carNo" column="car_no"/>
        <result property="recipientContact" column="recipient_contact"/>
        <result property="remark" column="remark"/>
        <result property="pieces" column="pieces"/>
        <result property="totalCharge" column="total_charge"/>
        <result property="deliveryCharge" column="delivery_charge"/>
        <result property="otherCharge" column="other_charge"/>
        <result property="handlingCharge" column="handling_charge"/>
        <result property="upstairsCharge" column="upstairs_charge"/>
        <result property="otherRemark" column="other_remark"/>
        <result property="orderTime" column="order_time"/>
        <result property="orderStatus" column="order_status"/>
        <result property="statusRemark" column="status_remark"/>
        <result property="createOpenid" column="create_openid"/>
        <result property="riderId" column="rider_id"/>
        <result property="riderName" column="rider_name"/>
        <result property="riderContact" column="rider_contact"/>
        <result property="referralCode" column="referral_code"/>
        <collection property="mediaUrlList" ofType="java.lang.String" resultMap="OrderMedia_Result"/>
        <collection property="jobVoList" ofType="com.senox.tms.vo.BicycleDeliveryOrderJobVo" resultMap="DeliveryOrderJobVo_Result"/>
        <collection property="orderGoodsDetailVos" ofType="com.senox.tms.vo.BicycleOrderGoodsDetailVo" resultMap="OrderGoodsDetailVo_Result"/>
        <collection property="detailItemVos" ofType="com.senox.tms.vo.BicycleDeliveryOrderDetailItemVo" resultMap="DeliveryOrderDetailVo_Item_Result"/>
    </resultMap>

    <resultMap id="OrderMedia_Result" type="java.lang.String">
        <constructor>
            <arg column="order_media_url"/>
        </constructor>
    </resultMap>

    <resultMap id="DeliveryOrderJobVo_Result" type="com.senox.tms.vo.BicycleDeliveryOrderJobVo">
        <result property="id" column="job_id"/>
        <result property="deliveryOrderDetailId" column="delivery_order_detail_id"/>
        <result property="status" column="job_status"/>
        <result property="remark" column="job_remark"/>
        <result property="modifiedTime" column="modified_time"/>
        <collection property="mediaUrlList" ofType="java.lang.String">
            <constructor>
                <arg column="media_url"/>
            </constructor>
        </collection>
    </resultMap>

    <resultMap id="OrderGoodsDetailVo_Result" type="com.senox.tms.vo.BicycleOrderGoodsDetailVo">
        <result property="id" column="goods_detail_id"/>
        <result property="orderId" column="order_id"/>
        <result property="goodsType" column="goods_type"/>
        <result property="weight" column="weight"/>
        <result property="size" column="size"/>
        <result property="pieces" column="detail_pieces"/>
        <result property="goodsName" column="goods_name"/>
    </resultMap>

    <resultMap id="DeliveryOrderDetailVo_Item_Result" type="com.senox.tms.vo.BicycleDeliveryOrderDetailItemVo">
        <result property="id" column="item_id"/>
        <result property="deliveryOrderSerialNo" column="delivery_order_serial_no"/>
        <result property="deliveryOrderSerialNoItem" column="delivery_order_serial_no_item"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="goodsId" column="item_goods_id"/>
        <result property="goodsType" column="item_goods_type"/>
        <result property="goodsName" column="item_goods_name"/>
        <result property="pieces" column="item_pieces"/>
        <result property="weight" column="item_weight"/>
        <result property="size" column="item_size"/>
    </resultMap>


    <select id="findDeliveryOrderDetailById" parameterType="java.lang.Long" resultMap="DeliveryOrderDetailVo_Result">
        select tbdod.id, tbdod.delivery_order_serial_no, tbdod.delivery_order_serial_no_item, tbdod.order_serial_no, tbdod.status, tbdod.picking_point, tbdod.delivery_point, tbdod.picking_time, tbdod.send_time
            , tbdod.receiving_time, tbdod.finish_time, tbdod.rating, tbo.start_point_name, tbo.end_point_name, tbo.send_time_start, tbo.sender_serial_no, tbo.car_no, tbdod.picked_pieces
            , tbo.send_time_end, tbo.sender_id, tbo.sender, tbo.sender_contact, tbo.recipient, tbo.recipient_contact, tbo.remark, tbo.pieces, tbo.total_charge, tbdoj.delivery_order_detail_id
            , tbo.delivery_charge, tbo.other_charge, tbo.handling_charge, tbo.upstairs_charge, tbdoj.id as job_id, tbdoj.status as job_status, tbdoj.remark as job_remark, tbdojm.media_url, tbogd.id as goods_detail_id
            , tbogd.order_id, tbogd.pieces as detail_pieces, tbogd.goods_name, tbo.order_time, tbdod.rider_id, tbr.name as rider_name, tbr.contact as rider_contact, tbdoj.modified_time
            , tbo.start_point_detail_name, tbo.end_point_detail_name, tbo.start_point_id, tbo.end_point_id, tbom.media_url as order_media_url, tbo.create_openid, tbo.other_remark, tbo.status as order_status, tbo.status_remark
            , tbogd.goods_type, tbogd.weight, tbogd.size, tbdodi.goods_id as item_goods_id, tbdodi.goods_name as item_goods_name, tbdodi.goods_type as item_goods_type
            , tbdodi.pieces as item_pieces, tbdodi.weight as item_weight, tbdodi.size as item_size, tbdodi.id as item_id, tbo.charges_id, tbr.referral_code
            from t_bicycle_delivery_order_detail tbdod
                inner join t_bicycle_delivery_order tbdo on tbdod.delivery_order_serial_no = tbdo.delivery_order_serial_no
                inner join t_bicycle_rider tbr on tbr.id = tbdod.rider_id
                inner join t_bicycle_order tbo on tbo.order_serial_no = tbdod.order_serial_no
                inner join t_bicycle_order_goods_detail tbogd on tbogd.order_id = tbo.id
                left join t_bicycle_delivery_order_detail_item tbdodi on tbdodi.delivery_order_serial_no_item = tbdod.delivery_order_serial_no_item
                left join t_bicycle_delivery_order_job tbdoj on tbdoj.delivery_order_detail_id = tbdod.id
                left join t_bicycle_delivery_order_job_media tbdojm on tbdojm.job_id = tbdoj.id
                LEFT JOIN t_bicycle_order_media tbom ON tbom.order_id = tbo.id
        where tbdod.id = #{id} and tbo.state = 1
    </select>

    <select id="findDeliveryOrderDetailByOrderSerialNo" parameterType="java.lang.String" resultMap="DeliveryOrderDetailVo_Result">
        select tbdod.id, tbdod.delivery_order_serial_no, tbdod.delivery_order_serial_no_item, tbdod.order_serial_no, tbdod.status, tbdod.picking_point, tbdod.delivery_point, tbdod.picking_time, tbdod.send_time
             , tbdod.receiving_time, tbdod.finish_time, tbdod.rating, tbo.start_point_name, tbo.end_point_name, tbo.send_time_start, tbo.sender_serial_no, tbo.car_no, tbdod.picked_pieces
             , tbo.send_time_end, tbo.sender_id, tbo.sender, tbo.sender_contact, tbo.recipient, tbo.recipient_contact, tbo.remark, tbo.pieces, tbo.total_charge, tbdoj.delivery_order_detail_id
             , tbo.delivery_charge, tbo.other_charge, tbo.handling_charge, tbo.upstairs_charge, tbdoj.id as job_id, tbdoj.status as job_status, tbdoj.remark as job_remark, tbdojm.media_url, tbogd.id as goods_detail_id
             , tbogd.order_id, tbogd.pieces as detail_pieces, tbogd.goods_name, tbo.order_time, tbdod.rider_id, tbr.name as rider_name, tbr.contact as rider_contact, tbdoj.modified_time
             , tbo.start_point_detail_name, tbo.end_point_detail_name, tbo.start_point_id, tbo.end_point_id, tbom.media_url as order_media_url, tbo.create_openid, tbo.other_remark, tbo.status as order_status, tbo.status_remark
             , tbogd.goods_type, tbogd.weight, tbogd.size, tbdodi.goods_id as item_goods_id, tbdodi.goods_name as item_goods_name, tbdodi.goods_type as item_goods_type
            , tbdodi.pieces as item_pieces, tbdodi.weight as item_weight, tbdodi.size as item_size, tbdodi.id as item_id, tbo.charges_id, tbr.referral_code
        from t_bicycle_delivery_order_detail tbdod
                 inner join t_bicycle_delivery_order tbdo on tbdod.delivery_order_serial_no = tbdo.delivery_order_serial_no
                 inner join t_bicycle_rider tbr on tbr.id = tbdod.rider_id
                 inner join t_bicycle_order tbo on tbo.order_serial_no = tbdod.order_serial_no
                 inner join t_bicycle_order_goods_detail tbogd on tbogd.order_id = tbo.id
                 left join t_bicycle_delivery_order_detail_item tbdodi on tbdodi.delivery_order_serial_no_item = tbdod.delivery_order_serial_no_item
                 left join t_bicycle_delivery_order_job tbdoj on tbdoj.delivery_order_detail_id = tbdod.id
                 left join t_bicycle_delivery_order_job_media tbdojm on tbdojm.job_id = tbdoj.id
                 LEFT JOIN t_bicycle_order_media tbom ON tbom.order_id = tbo.id
        where tbdod.order_serial_no = #{orderSerialNo} and tbo.state = 1
    </select>

    <select id="riderCountByRiderId" resultType="com.senox.tms.vo.BicycleRiderCountVo">
        select bp.rider_id    as rider_id,
            curdate()    as bill_date,
            bp.payee_name  as rider_name,
            ifnull(count(DISTINCT bp.order_serial_no), 0)          today_count,
            ifnull(sum(bp.referral_amount), 0) as today_referral_income,
            ifnull(sum(bp.referral), 0) as today_referral_count,
            ifnull(sum(bp.share_amount), 0) as today_delivery_income,
            ifnull(sum(bp.referral_amount + bp.share_amount), 0) as today_income,
            ifnull(sum(bp.pieces), 0) as today_pieces,
            avg(bp.delivery_time) as avg_delivery_time
        from t_bicycle_payoff bp
        WHERE
            bp.rider_id = #{riderId}
        <if test="startTime == null and endTime == null">
            and date(bp.create_time) = curdate()
        </if>
        <if test="startTime != null">
            AND bp.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND bp.create_time <![CDATA[<=]]> #{endTime}
        </if>
        group by bp.rider_id, bp.payee_name
    </select>

    <select id="riderCount" parameterType="com.senox.tms.vo.BicycleRiderCountSearchVo" resultType="int">
        select count(1)
        from (select count(1)
        from t_bicycle_payoff bp
        <where>
            and date(bp.create_time) = curdate()
            <if test="riderName != null and riderName != ''">
                and bp.payee_name like concat('%', #{riderName}, '%')
            </if>
        </where>
        group by bp.rider_id
        ) res
    </select>

    <select id="riderCountList" parameterType="com.senox.tms.vo.BicycleRiderCountSearchVo" resultType="com.senox.tms.vo.BicycleRiderCountVo">
        select bp.rider_id    as rider_id,
               curdate()    as bill_date,
               bp.payee_name  as rider_name,
               ifnull(count(DISTINCT bp.order_serial_no), 0)          today_count,
               ifnull(sum(bp.referral), 0) as today_referral_count,
               ifnull(sum(bp.referral_amount), 0) as today_referral_income,
               ifnull(sum(bp.share_amount), 0) as today_delivery_income,
               ifnull(sum(bp.referral_amount + bp.share_amount), 0) as today_income,
               ifnull(sum(bp.pieces), 0) as today_pieces,
               avg(bp.delivery_time) as avg_delivery_time
        from t_bicycle_payoff bp
        <where>
            and date(bp.create_time) = curdate()
            <if test="riderName != null and riderName != ''">
                and bp.payee_name like concat('%', #{riderName}, '%')
            </if>
        </where>
        group by bp.rider_id, bp.payee_name
        <choose>
            <when test="orderStr != null and orderStr != ''">
                order by ${orderStr}
            </when>
            <otherwise>
                order by bp.payee_name desc
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumRiderCount" parameterType="com.senox.tms.vo.BicycleRiderCountSearchVo" resultType="com.senox.tms.vo.BicycleRiderCountVo">
        select ifnull(count(DISTINCT bp.order_serial_no), 0)          today_count,
               ifnull( count( bp.order_serial_no ), 0 ) today_detail_count,
               ifnull(sum(bp.referral_amount), 0) as today_referral_income,
               ifnull(sum(bp.share_amount), 0) as today_delivery_income,
               ifnull(sum(bp.referral_amount + bp.share_amount), 0) as today_income,
               ifnull(sum(bp.pieces), 0) as today_pieces
        from t_bicycle_payoff bp
        <where>
            and date(bp.create_time) = curdate()
        </where>
        <if test="riderName != null and riderName != ''">
              and bp.payee_name like concat('%', #{riderName}, '%')
        </if>
    </select>

    <select id="riderHistoryCount" parameterType="com.senox.tms.vo.BicycleRiderCountSearchVo" resultType="int">
        select count(1)
        from t_bicycle_payoff_day_report tbpdr
                 inner join t_bicycle_rider tbr on tbr.id = tbpdr.payee_id
            <where>
                <if test="riderName != null and riderName != ''">
                    AND tbr.name LIKE CONCAT('%', #{riderName}, '%')
                </if>
                <if test="riderId != null">
                    AND tbr.id = #{riderId}
                </if>
                <if test="billDateStart != null">
                    AND tbpdr.year_month_day >= #{billDateStart}
                </if>
                <if test="billDateEnd != null">
                    AND tbpdr.year_month_day <![CDATA[<=]]> #{billDateEnd}
                </if>
            </where>
    </select>

    <select id="riderHistoryCountList" parameterType="com.senox.tms.vo.BicycleRiderCountSearchVo" resultType="com.senox.tms.vo.BicycleRiderCountVo">
        select tbpdr.id                as id,
               tbr.id                  as rider_id,
               tbpdr.year_month_day    as bill_date,
               tbr.name                as rider_name,
               tbpdr.order_number as today_count,
               tbpdr.pieces       as today_pieces,
               tbpdr.referral_count as today_referral_count,
               tbpdr.referral_amount as today_referral_income,
               tbpdr.total_amount - tbpdr.referral_amount as today_delivery_income,
               tbpdr.total_amount as today_income,
               tbpdr.avg_delivery_time
        from t_bicycle_payoff_day_report tbpdr
        inner join t_bicycle_rider tbr on tbr.id = tbpdr.payee_id
            <where>
                <if test="riderName != null and riderName != ''">
                    and tbr.name like concat('%', #{riderName}, '%')
                </if>
                <if test="riderId != null">
                    and tbr.id = #{riderId}
                </if>
                <if test="billDateStart != null">
                    and tbpdr.year_month_day >= #{billDateStart}
                </if>
                <if test="billDateEnd != null">
                    and tbpdr.year_month_day <![CDATA[<=]]> #{billDateEnd}
                </if>
            </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                order by ${orderstr}
            </when>
            <otherwise>
                order by tbpdr.year_month_day desc
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumRiderHistoryCount" parameterType="com.senox.tms.vo.BicycleRiderCountSearchVo" resultType="com.senox.tms.vo.BicycleRiderCountVo">
        SELECT
            ifnull(SUM(tbpdr.order_number), 0) AS today_count,
            ifnull(SUM(tbpdr.pieces), 0) AS today_pieces,
            ifnull(SUM(tbpdr.referral_count), 0) as today_referral_count,
            ifnull(sum(tbpdr.referral_amount), 0) as today_referral_income,
            ifnull(sum(tbpdr.total_amount - tbpdr.referral_amount), 0) as today_delivery_income,
            ifnull(SUM(tbpdr.total_amount), 0) AS today_income,
            ifnull(SUM(tbpdr.avg_delivery_time), 0) AS  avg_delivery_time
        FROM t_bicycle_payoff_day_report tbpdr
        INNER JOIN t_bicycle_rider tbr ON tbr.id = tbpdr.payee_id
        <where>
            <if test="riderName != null and riderName != ''">
                AND tbr.name LIKE CONCAT('%', #{riderName}, '%')
            </if>
            <if test="riderId != null">
                AND tbr.id = #{riderId}
            </if>
            <if test="billDateStart != null">
                AND tbpdr.year_month_day >= #{billDateStart}
            </if>
            <if test="billDateEnd != null">
                AND tbpdr.year_month_day <![CDATA[<=]]> #{billDateEnd}
            </if>
        </where>
    </select>

    <select id="listDayBestRider" resultType="com.senox.tms.vo.BicycleDayBestRiderVo">
        SELECT tbr.NAME AS rider_name, sum( tbdod.picked_pieces ) as pieces, count(tbdod.id) order_count FROM t_bicycle_delivery_order_detail tbdod
        INNER JOIN t_bicycle_rider tbr ON tbdod.rider_id = tbr.id
        INNER JOIN t_bicycle_order tbo ON tbo.order_serial_no = tbdod.order_serial_no
        WHERE
            tbdod.STATUS IN ( 6, 7 ) and tbo.state = 1 and tbo.status = 0
            AND tbdod.receiving_time >= #{startTime}
            AND tbdod.receiving_time <![CDATA[<=]]> #{endTime}
        GROUP BY tbr.id
        ORDER BY sum( tbdod.picked_pieces ) DESC
        LIMIT 3
    </select>

    <select id="listOrderDoingAndDoneCount" resultType="com.senox.tms.vo.BicycleOrderCountVo">
        SELECT t2.hours, t3.doing_count, t3.done_count, t3.pieces
        FROM
        (
            SELECT  DATE_FORMAT(DATE_SUB( #{endTime}, INTERVAL ( number * 1 ) ${searchType} ), #{searchTypeFormat}) AS hours FROM
                (
                    ${searchResultFormat}
                ) as t1
        ) AS t2
        LEFT JOIN (
            SELECT DATE_FORMAT ( tbdo.create_time, #{searchTypeFormat} ) AS hour_of_order,
                SUM( CASE WHEN tbdo.STATUS IN ( 0, 1, 2, 3, 4, 5 ) THEN 1 ELSE 0 END ) AS doing_count,
                SUM( CASE WHEN tbdo.STATUS IN ( 6, 7 ) THEN 1 ELSE 0 END ) AS done_count,
                SUM(tbo.pieces) as pieces
            FROM t_bicycle_delivery_order tbdo
                INNER JOIN t_bicycle_bill tbb on tbb.delivery_order_serial_no = tbdo.delivery_order_serial_no
                INNER JOIN t_bicycle_order tbo on tbo.order_serial_no = tbb.order_serial_no
            WHERE tbdo.create_time >= #{startTime} AND tbdo.create_time <![CDATA[<=]]> #{endTime}
            GROUP BY hour_of_order
        ) AS t3 ON t3.hour_of_order = t2.hours
    </select>

    <select id="listOrderUnDoCount" resultType="com.senox.tms.vo.BicycleOrderCountVo">
        SELECT t2.hours, t3.undo_count, t3.pieces from
        (
            SELECT  DATE_FORMAT(DATE_SUB( #{endTime}, INTERVAL ( number * 1 ) ${searchType} ), #{searchTypeFormat}) AS hours FROM
                (
                    ${searchResultFormat}
                ) as t1
        ) AS t2
        LEFT JOIN (
            SELECT DATE_FORMAT ( tbo.order_time, #{searchTypeFormat} ) AS hour_of_order,
                SUM( CASE WHEN tbdod.delivery_order_serial_no IS NULL THEN 1 ELSE 0 END ) AS undo_count,
                SUM(CASE WHEN tbdod.delivery_order_serial_no IS NULL THEN tbo.pieces ELSE 0 END) as pieces
            FROM t_bicycle_order tbo
                LEFT JOIN t_bicycle_delivery_order_detail tbdod ON tbdod.order_serial_no = tbo.order_serial_no
            WHERE tbo.state = 1 and tbo.status = 0 AND tbo.order_time >= #{startTime} AND tbo.order_time <![CDATA[<=]]> #{endTime}
            GROUP BY hour_of_order
        )AS t3 ON t3.hour_of_order = t2.hours
    </select>

    <select id="undoDeliveryOrderCount" resultType="java.lang.Integer">
        select count(1) as undo_count from t_bicycle_delivery_order_detail tbdod
        WHERE tbdod.status in (1, 2, 3, 4, 5) and tbdod.rider_id = #{riderId}
    </select>

    <!-- 获取最大的子配送单号 -->
    <select id="findMaxDeliveryOrderItemNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(delivery_order_serial_no_item) FROM t_bicycle_delivery_order_detail WHERE delivery_order_serial_no_item LIKE CONCAT(#{prefix}, '%');
    </select>

    <select id="countDeliveryDetail" parameterType="com.senox.tms.vo.BicycleDeliverySearchVo" resultType="int">
        select count(tbdod.id) from t_bicycle_delivery_order_detail tbdod
            inner join t_bicycle_order tbo on tbo.order_serial_no = tbdod.order_serial_no
        <where>
            <if test="orderStatus != null">
                and tbo.status = #{orderStatus}
            </if>
            <if test="state != null">
                AND tbo.state = #{state}
            </if>
            <if test="status != null and status.size > 0">
                AND tbdod.status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="riderId != null">
                AND tbdod.rider_id = #{riderId}
            </if>
        </where>
    </select>

    <select id="listDeliveryDetail" parameterType="com.senox.tms.vo.BicycleDeliverySearchVo" resultType="com.senox.tms.vo.BicycleDeliveryOrderDetailVo">
        select tbdod.id, tbdod.delivery_order_serial_no, tbdod.order_serial_no, tbdod.status, tbdod.picking_point, tbdod.delivery_point
                , tbdod.picking_time, tbdod.send_time, tbdod.receiving_time, tbdod.finish_time, tbdod.rating, tbo.start_point_name
                , tbo.end_point_name, tbo.send_time_start, tbo.sender_serial_no, tbo.car_no, tbdod.picked_pieces, tbo.send_time_end
                , tbo.sender_id, tbo.sender, tbo.sender_contact, tbo.recipient, tbo.recipient_contact, tbo.remark, tbo.pieces, tbo.total_charge
                , tbo.delivery_charge, tbo.other_charge, tbo.handling_charge, tbo.upstairs_charge, tbo.order_time, tbdod.rider_id
                , tbr.name as rider_name, tbr.contact as rider_contact, tbo.start_point_detail_name, tbo.end_point_detail_name, tbo.start_point_id
                , tbo.end_point_id, tbo.other_remark, tbo.status as order_status, tbo.status_remark, tbr.referral_code
            from t_bicycle_delivery_order_detail tbdod
            inner join t_bicycle_order tbo on tbo.order_serial_no = tbdod.order_serial_no
            inner join t_bicycle_rider tbr on tbr.id = tbdod.rider_id
        <where>
            <if test="orderStatus != null">
                and tbo.status = #{orderStatus}
            </if>
            <if test="state != null">
                AND tbo.state = #{state}
            </if>
            <if test="status != null and status.size > 0">
                AND tbdod.status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="riderId != null">
                AND tbdod.rider_id = #{riderId}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY tbdod.receiving_time desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 根据订单编号查询配送订单详细信息 -->
    <select id="deliveryDetailInfoByOrderSerialNo" parameterType="java.lang.String" resultType="com.senox.tms.vo.BicycleDeliveryDetailInfoVo">
        SELECT bdod.id as delivery_order_detail_id
             , bdod.order_serial_no
             , bdod.delivery_order_serial_no_item
             , bdod.picked_pieces
             , bdod.rider_id
             , br.`name` as rider_name
             , bdod.status
        from t_bicycle_delivery_order_detail bdod
        INNER JOIN t_bicycle_rider br on br.id = bdod.rider_id
        WHERE bdod.order_serial_no = #{orderSerialNo}
    </select>
</mapper>
