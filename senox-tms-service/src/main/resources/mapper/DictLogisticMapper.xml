<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.DictLogisticMapper">

    <resultMap id="logisticResultMap" type="com.senox.tms.vo.DictLogisticVo">
        <id property="id" column="id"/>
        <result property="key" column="key"/>
        <result property="name" column="name"/>
        <result property="category" column="category" typeHandler="com.senox.tms.handler.BaseEnumTypeHandler"/>
        <result property="attr1" column="attr1"/>
        <result property="attr2" column="attr2"/>
        <result property="creatorName" column="creator_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <insert id="addBatch" useGeneratedKeys="true" keyProperty="id">
        insert into t_dict_logistic(`key`,name,category,attr1,attr2, creator_id, creator_name, create_time,
        modifier_id, modifier_name, modified_time)
        values
        <foreach collection="dictLogistics" item="item" separator=",">
            (
            #{item.key},
            #{item.name},
            #{item.category},
            #{item.attr1},
            #{item.attr2},
            #{item.creatorId},
            #{item.creatorName},
            now(),
            #{item.modifierId},
            #{item.modifierName},
            now())
        </foreach>
    </insert>

    <update id="updateBatch">
        update t_dict_logistic
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`key` = case" suffix="end,">
                <foreach collection="dictLogistics" item="item">
                    <if test="null != item.key">
                        when id = #{item.id} then #{item.key}
                    </if>
                </foreach>
            </trim>
            <trim prefix="name = case" suffix="end,">
                <foreach collection="dictLogistics" item="item">
                    <if test="null != item.name">
                        when id = #{item.id} then #{item.name}
                    </if>
                </foreach>
            </trim>
            <trim prefix="category = case" suffix="end,">
                <foreach collection="dictLogistics" item="item">
                    <if test="null != item.category">
                        when id = #{item.id} then #{item.category}
                    </if>
                </foreach>
            </trim>
            <trim prefix="attr1 = case" suffix="end,">
                <foreach collection="dictLogistics" item="item">
                    <if test="null != item.attr1">
                        when id = #{item.id} then #{item.attr1}
                    </if>
                </foreach>
            </trim>
            <trim prefix="attr2 = case" suffix="end,">
                <foreach collection="dictLogistics" item="item">
                    <if test="null != item.attr2">
                        when id = #{item.id} then #{item.attr2}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = case" suffix="end,">
                <foreach collection="dictLogistics" item="item">
                    <if test="null != item.modifierId">
                        when id = #{item.id} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = case" suffix="end,">
                <foreach collection="dictLogistics" item="item">
                    <if test="null != item.modifierName">
                        when id = #{item.id} then #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = now()
        </trim>
        <where>
            and id in
            <foreach collection="dictLogistics" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </where>
    </update>

    <select id="list" resultMap="logisticResultMap">
        select id,
               `key`,
               name,
               category,
               attr1,
               attr2,
               creator_id,
               creator_name,
               create_time,
               modifier_id,
               modifier_name,
               modified_time
        from t_dict_logistic
        <where>
            <if test="null != ids and ids.size() > 0">
                and id in
                <foreach collection="ids" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="null != keys and keys.size() > 0">
                and `key` in
                <foreach collection="keys" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="null != name and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="null != attr1 and attr1 != ''">
                and attr1 like concat('%',#{attr1},'%')
            </if>
            <if test="null != attr2 and attr2 != ''">
                and attr2 like concat('%',#{attr2},'%')
            </if>
            <if test="null != category">
                and category =  #{category.number}
            </if>
            and is_disabled = false
        </where>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="countList" resultType="int">
        select count(*) from t_dict_logistic
        <where>
            <if test="null != keys and keys.size() > 0">
                and `key` in
                <foreach collection="keys" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="null != name and name != ''">
                and name like concat('%',#{name},'%')
            </if>
            <if test="null != category">
                and category = #{category.number}
            </if>
            and is_disabled = false
        </where>
    </select>
</mapper>
