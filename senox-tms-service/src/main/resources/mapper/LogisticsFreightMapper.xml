<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticsFreightMapper">

    <select id="list" resultType="com.senox.tms.domain.LogisticsFreight">
        select lf.id,
               operations_department,
               sender_customer_name,
               sender_customer_contact,
               sender_pieces,
               sender_freight_charge,
               sender_settlement_type,
               receiving_date,
               receiving_no,
               receiving_customer_name,
               receiving_customer_address,
               receiving_customer_contact,
               transfer_logistics_company,
               transfer_logistics_no,
               transfer_charge,
               profit_amount,
               lf.remark,
               au.real_name as creator_name,
               lf.create_time
        from t_logistics_freight lf
        inner join u_admin_user au on au.id = lf.creator_id
        <where>
            <if test="null != senderCustomerName and senderCustomerName != ''">
                and sender_customer_name like concat('%',#{senderCustomerName},'%')
            </if>
            <if test="null != receivingCustomerName and receivingCustomerName != ''">
                and receiving_customer_name like concat('%',#{receivingCustomerName},'%')
            </if>
            <if test="null != receivingNo and receivingNo != ''">
                and receiving_no like concat('%',#{receivingNo},'%')
            </if>
            <if test="null != transferLogisticsCompany and transferLogisticsCompany != ''">
                and transfer_logistics_company like concat('%',#{transferLogisticsCompany},'%')
            </if>
            <if test="null != receivingStartDate">
                and receiving_date >= #{receivingStartDate}
            </if>
            <if test="null != receivingEndDate">
                and receiving_date &lt;= #{receivingEndDate}
            </if>
            <if test="null != senderSettlementType">
                and sender_settlement_type = #{senderSettlementType}
            </if>
        </where>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="countList" resultType="int">
        select count(id)
        from t_logistics_freight
        <where>
            <if test="null != senderCustomerName and senderCustomerName != ''">
                and sender_customer_name like concat('%',#{senderCustomerName},'%')
            </if>
            <if test="null != receivingCustomerName and receivingCustomerName != ''">
                and receiving_customer_name like concat('%',#{receivingCustomerName},'%')
            </if>
            <if test="null != receivingNo and receivingNo != ''">
                and receiving_no like concat('%',#{receivingNo},'%')
            </if>
            <if test="null != transferLogisticsCompany and transferLogisticsCompany != ''">
                and transfer_logistics_company like concat('%',#{transferLogisticsCompany},'%')
            </if>
            <if test="null != receivingStartDate">
                and receiving_date >= #{receivingStartDate}
            </if>
            <if test="null != receivingEndDate">
                and receiving_date &lt;= #{receivingEndDate}
            </if>
            <if test="null != senderSettlementType">
                and sender_settlement_type = #{senderSettlementType}
            </if>
        </where>
    </select>

    <select id="listTotalAmount" resultType="com.senox.tms.vo.LogisticsFreightStatisticsVo">
        select sum(sender_pieces) as total_sender_pieces,
               sum(sender_freight_charge) as total_sender_freight_charge,
               sum(transfer_charge) as total_transfer_charge,
               sum(profit_amount) as total_profit_amount
        from t_logistics_freight
        <where>
            <if test="null != senderCustomerName and senderCustomerName != ''">
                and sender_customer_name like concat('%',#{senderCustomerName},'%')
            </if>
            <if test="null != receivingCustomerName and receivingCustomerName != ''">
                and receiving_customer_name like concat('%',#{receivingCustomerName},'%')
            </if>
            <if test="null != receivingNo and receivingNo != ''">
                and receiving_no like concat('%',#{receivingNo},'%')
            </if>
            <if test="null != transferLogisticsCompany and transferLogisticsCompany != ''">
                and transfer_logistics_company like concat('%',#{transferLogisticsCompany},'%')
            </if>
            <if test="null != receivingStartDate">
                and receiving_date >= #{receivingStartDate}
            </if>
            <if test="null != receivingEndDate">
                and receiving_date &lt;= #{receivingEndDate}
            </if>
            <if test="null != senderSettlementType">
                and sender_settlement_type = #{senderSettlementType}
            </if>
        </where>
    </select>

    <select id="findById" resultType="com.senox.tms.domain.LogisticsFreight">
        select lf.id,
        operations_department,
        sender_customer_name,
        sender_customer_contact,
        sender_pieces,
        sender_freight_charge,
        sender_settlement_type,
        receiving_date,
        receiving_no,
        receiving_customer_name,
        receiving_customer_address,
        receiving_customer_contact,
        transfer_logistics_company,
        transfer_logistics_no,
        transfer_charge,
        profit_amount,
        remark,
        lf.remark,
        au.real_name as creator_name,
        lf.create_time
        from t_logistics_freight lf
        inner join u_admin_user au on au.id = lf.creator_id
        <where>
            and lf.is_disabled = false
            and lf.id = #{id}
        </where>
    </select>

</mapper>
