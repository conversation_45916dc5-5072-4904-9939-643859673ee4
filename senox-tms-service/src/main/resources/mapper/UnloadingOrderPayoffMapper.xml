<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingOrderPayoffMapper">

    <select id="countPayoff" parameterType="com.senox.tms.vo.UnloadingOrderPayoffSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_order_payoff
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerName != null and workerName != ''">
                AND worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="payoffDateStart != null">
                AND payoff_date >= #{payoffDateStart}
            </if>
            <if test="payoffDateEnd != null">
                AND payoff_date <![CDATA[<=]]> #{payoffDateEnd}
            </if>
            <if test="payoffTimeStart != null">
                AND payoff_time >= #{payoffTimeStart}
            </if>
            <if test="payoffTimeEnd != null">
                AND payoff_time <![CDATA[<=]]> #{payoffTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="sumPayoff" parameterType="com.senox.tms.vo.UnloadingOrderPayoffSearchVo" resultType="com.senox.tms.domain.UnloadingOrderPayoff">
        SELECT
             IFNULL(SUM(payoff_amount), 0) as payoff_amount
        FROM px_unloading_order_payoff
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerName != null and workerName != ''">
                AND worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="payoffDateStart != null">
                AND payoff_date >= #{payoffDateStart}
            </if>
            <if test="payoffDateEnd != null">
                AND payoff_date <![CDATA[<=]]> #{payoffDateEnd}
            </if>
            <if test="payoffTimeStart != null">
                AND payoff_time >= #{payoffTimeStart}
            </if>
            <if test="payoffTimeEnd != null">
                AND payoff_time <![CDATA[<=]]> #{payoffTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listPayoff" parameterType="com.senox.tms.vo.UnloadingOrderPayoffSearchVo" resultType="com.senox.tms.domain.UnloadingOrderPayoff">
        SELECT
              id
            , payoff_date
            , payoff_year
            , payoff_month
            , order_id
            , order_no
            , worker_id
            , worker_no
            , worker_name
            , payoff_amount
            , status
            , payoff_time
        FROM px_unloading_order_payoff
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerName != null and workerName != ''">
                AND worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="payoffDateStart != null">
                AND payoff_date >= #{payoffDateStart}
            </if>
            <if test="payoffDateEnd != null">
                AND payoff_date <![CDATA[<=]]> #{payoffDateEnd}
            </if>
            <if test="payoffTimeStart != null">
                AND payoff_time >= #{payoffTimeStart}
            </if>
            <if test="payoffTimeEnd != null">
                AND payoff_time <![CDATA[<=]]> #{payoffTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>
