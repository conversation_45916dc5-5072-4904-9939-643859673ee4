package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.BillStatus;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import com.senox.tms.constant.UnloadingOrderState;
import com.senox.tms.constant.UnloadingOrderWorkerStatus;
import com.senox.tms.domain.UnloadingOrderBill;
import com.senox.tms.mapper.UnloadingOrderBillMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.UnloadOrderSearchVo;
import com.senox.tms.vo.UnloadingMonthVo;
import com.senox.tms.vo.UnloadingOrderBillSearchVo;
import com.senox.tms.vo.UnloadingOrderVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/31 15:53
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UnloadingOrderBillService extends ServiceImpl<UnloadingOrderBillMapper, UnloadingOrderBill> {

    private final UnloadingOrderService unloadingOrderService;

    /**
     * 生成账单
     * @param monthVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateBill(UnloadingMonthVo monthVo) {
        List<UnloadingOrderVo> orders = listOrders(monthVo);
        List<UnloadingOrderBill> bills = listBills(orders);
        List<UnloadingOrderBill> dbBills = listBills(monthVo);

        // 比较数据库数据，分情况处理
        DataSepDto<UnloadingOrderBill> sepData = separateBill(dbBills, bills);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());

        } else if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchById(sepData.getUpdateList());

        } else if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            StringBuilder builder = new StringBuilder();
            sepData.getRemoveList().forEach(x -> builder.append(x.getOrderNo()).append("|").append(x.getBillDate()).append("\r\n"));

            if (builder.length() > 0) {
                builder.append("账单已缴费，不做更新。");
            }
            log.info(" --- 【账单生成】已缴费账单 --- {}", builder);
        }
    }

    private List<UnloadingOrderBill> listBills(List<UnloadingOrderVo> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }
        List<UnloadingOrderBill> bills = new ArrayList<>(orders.size());
        orders.forEach(order -> bills.add(generateBill(order)));
        return bills;
    }

    private UnloadingOrderBill generateBill(UnloadingOrderVo order) {
        LocalDate date = order.getOrderTime().toLocalDate();
        UnloadingOrderBill bill = new UnloadingOrderBill();
        bill.setBillDate(date);
        bill.setBillYear(date.getYear());
        bill.setBillMonth(date.getMonthValue());
        bill.setOrderNo(order.getOrderNo());
        bill.setAmount(order.getAmount());
        ContextUtils.initEntityCreator(bill);
        ContextUtils.initEntityModifier(bill);
        bill.setCreateTime(LocalDateTime.now());
        bill.setModifiedTime(LocalDateTime.now());
        return bill;
    }

    private List<UnloadingOrderVo> listOrders(UnloadingMonthVo monthVo) {
        UnloadOrderSearchVo searchVo = new UnloadOrderSearchVo();
        if (WrapperClassUtils.biggerThanInt(monthVo.getYear(), 0)
                && WrapperClassUtils.biggerThanInt(monthVo.getMonth(), 0)) {
            LocalDateTime startTime = DateUtils.parseDate(monthVo.getYear(), monthVo.getMonth(), 1).atStartOfDay();
            LocalDateTime endTime = DateUtils.parseDate(monthVo.getYear(), monthVo.getMonth() + 1, 1, true).minusDays(1).atStartOfDay();
            searchVo.setOrderTimeStart(startTime);
            searchVo.setOrderTimeEnd(endTime);
        }
        searchVo.setOrderNo(monthVo.getOrderNo());
        searchVo.setOrderNoList(monthVo.getOrderNoList());
        searchVo.setState(UnloadingOrderState.NORMAL.getNumber());
        searchVo.setWorkerStatusList(Collections.singletonList(UnloadingOrderWorkerStatus.DONE.getNumber()));
        searchVo.setPage(false);
        return unloadingOrderService.listOrder(searchVo);
    }

    private List<UnloadingOrderBill> listBills(UnloadingMonthVo monthVo) {
        UnloadingOrderBillSearchVo searchVo = new UnloadingOrderBillSearchVo();
        if (WrapperClassUtils.biggerThanInt(monthVo.getYear(), 0)
                && WrapperClassUtils.biggerThanInt(monthVo.getMonth(), 0)) {
            searchVo.setBillDateStart(DateUtils.parseDate(monthVo.getYear(), monthVo.getMonth(), 1));
            searchVo.setBillDateEnd(DateUtils.parseDate(monthVo.getYear(), monthVo.getMonth() + 1, 1, true).minusDays(1));
        }
        searchVo.setOrderNo(monthVo.getOrderNo());
        searchVo.setOrderNoList(monthVo.getOrderNoList());
        searchVo.setPage(false);
        return listBill(searchVo);
    }

    /**
     * 账单比较
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<UnloadingOrderBill> separateBill(List<UnloadingOrderBill> srcList,
                                                        List<UnloadingOrderBill> targetList) {
        List<UnloadingOrderBill> addList = new ArrayList<>(targetList.size());
        List<UnloadingOrderBill> updateList = new ArrayList<>(targetList.size());
        List<UnloadingOrderBill> removeList = new ArrayList<>(targetList.size());

        if (CollectionUtils.isEmpty(srcList)) {
            addList = targetList;

        } else {
            Map<String, UnloadingOrderBill> srcMap = srcList.stream()
                    .collect(Collectors.toMap(UnloadingOrderBill::getOrderNo, Function.identity()));

            for (UnloadingOrderBill item : targetList) {

                UnloadingOrderBill srcItem = srcMap.get(item.getOrderNo());
                if (srcItem == null) {
                    // 新增
                    addList.add(item);

                } else {
                    if (BillStatus.fromValue(srcItem.getStatus()) != BillStatus.PAID) {
                        // 未缴费，可以更新
                        item.setId(srcItem.getId());
                        item.setCreatorId(null);
                        item.setCreatorName(null);
                        item.setCreateTime(null);
                        updateList.add(item);
                    } else {
                        // 提示不更新
                        removeList.add(item);
                    }
                }
            }
        }

        return new DataSepDto<>(addList, updateList, removeList);
    }

    /**
     * 支付应收账单
     * @param id
     */
    public void payUnloadingOrderBill(Long id) {
        UnloadingOrderBill orderBill = findById(id);
        checkStatus(orderBill);
        orderBill.setPaidTime(LocalDateTime.now());
        orderBill.setStatus(UnloadingOrderBillPayoffStatus.PAID.getNumber());
        ContextUtils.initEntityModifier(orderBill);
        orderBill.setModifiedTime(LocalDateTime.now());
        updateById(orderBill);
    }

    /**
     * 根据id查询应收账单
     * @param id
     * @return
     */
    public UnloadingOrderBill findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据id删除应收账单
     * @param id
     */
    public void deleteById(Long id) {
        UnloadingOrderBill orderBill = findById(id);
        checkStatus(orderBill);
        removeById(id);
    }

    /**
     * 根据订单编号查询应收账单
     * @param orderNo
     * @return
     */
    public UnloadingOrderBill findByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }
        return getOne(new QueryWrapper<UnloadingOrderBill>().lambda()
                .eq(UnloadingOrderBill::getOrderNo, orderNo));
    }

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    public int countBill(UnloadingOrderBillSearchVo searchVo) {
        return getBaseMapper().countBill(searchVo);
    }

    /**
     * 列表
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderBill> listBill(UnloadingOrderBillSearchVo searchVo) {
        return getBaseMapper().listBill(searchVo);
    }

    /**
     * 分佣分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderBill> pageOrderBill(UnloadingOrderBillSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> countBill(searchVo), () -> listBill(searchVo));
    }

    /**
     * 金额合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderBill sumBill(UnloadingOrderBillSearchVo searchVo) {
        return getBaseMapper().sumBill(searchVo);
    }

    /**
     * 更新订单应收金额
     * @param orderNo
     * @param amount
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderBill(String orderNo, BigDecimal amount) {
        UnloadingOrderBill orderBill = findByOrderNo(orderNo);
        if (orderBill == null) {
            log.info("订单未生成应收记录，只更新订单金额。。。");
            //update order
            unloadingOrderService.updateOrderAmount(orderNo, amount);
            return;
        }
        log.info("订单已生成应收记录，更新应收金额以及订单金额");
        checkStatus(orderBill);
        orderBill.setAmount(amount);
        ContextUtils.initEntityModifier(orderBill);
        orderBill.setModifiedTime(LocalDateTime.now());
        updateById(orderBill);
        //update order
        unloadingOrderService.updateOrderAmount(orderNo, amount);
    }

    /**
     * 状态检测
     * @param orderBill
     */
    public void checkStatus(UnloadingOrderBill orderBill) {
        if (orderBill == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "应收账单记录不存在!");
        }
        if (orderBill.getStatus() == UnloadingOrderBillPayoffStatus.PAID.getNumber()) {
            throw new BusinessException("账单已支付！");
        }
    }
}
