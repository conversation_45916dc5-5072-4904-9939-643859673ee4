package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.domain.UnloadingOrderWorkers;
import com.senox.tms.mapper.UnloadingOrderWorkersMapper;
import com.senox.tms.utils.ContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/20 16:49
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UnloadingOrderWorkersService extends ServiceImpl<UnloadingOrderWorkersMapper, UnloadingOrderWorkers> {

    private final UnloadingWorkerService workerService;

    /**
     * 保存订单搬运工明细
     * @param orderId
     * @param workers
     */
    public void saveWorkers(Long orderId, List<UnloadingOrderWorkers> workers) {
        log.info("【装卸订单id】----- {}， 搬运工明细:{}", orderId, JsonUtils.object2Json(workers));
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new BusinessException("装卸订单id有误");
        }
        workers.forEach(worker -> {
            worker.setOrderId(orderId);
            ContextUtils.initEntityModifier(worker);
            worker.setModifiedTime(LocalDateTime.now());
        });
        DataSepDto<UnloadingOrderWorkers> sepDto = SeparateUtils.separateData(listByOrderId(orderId), workers);
        log.info("转换后的sepDto值为 ---- {}", JsonUtils.object2Json(sepDto));
        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            sepDto.getAddList().forEach(worker -> {
                ContextUtils.initEntityCreator(worker);
                worker.setCreateTime(LocalDateTime.now());
            });
            saveBatch(sepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getRemoveList())) {
            removeByIds(sepDto.getRemoveList().stream().map(UnloadingOrderWorkers::getId).collect(Collectors.toList()));
            workerService.updateBatchWorkerStatus(sepDto.getRemoveList().stream().map(UnloadingOrderWorkers::getWorkerId).collect(Collectors.toList())
                    , UnloadingWorkerStatus.ALREADY_LISTED.getNumber());
        }
    }

    /**
     * 根据订单id查询搬运工
     * @param orderId
     * @return
     */
    public List<UnloadingOrderWorkers> listByOrderId(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return Collections.emptyList();
        }
        return listByOrderIds(Collections.singletonList(orderId));
    }


    /**
     * 根据订单id集合查询搬运工
     * @param orderIds
     * @return
     */
    public List<UnloadingOrderWorkers> listByOrderIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<UnloadingOrderWorkers>().in(UnloadingOrderWorkers::getOrderId, orderIds));
    }
}
