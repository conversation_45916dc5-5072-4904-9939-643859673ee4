package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.domain.LogisticStatisticsDayReport;
import com.senox.tms.mapper.LogisticStatisticsDayReportMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticStatisticsDayReportPageResult;
import com.senox.tms.vo.LogisticStatisticsDayReportSearchVo;
import com.senox.tms.vo.LogisticStatisticsDayReportVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/19 10:32
 */
@Service
@Slf4j
public class LogisticStatisticsDayReportService extends ServiceImpl<LogisticStatisticsDayReportMapper, LogisticStatisticsDayReport> {

    /**
     * 批量添加货物统计报表
     * @param reportList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<LogisticStatisticsDayReport> reportList) {
        List<LogisticStatisticsDayReport> dayReportList = listByLogisticStatisticsDayReport(reportList);
        DataSepDto<LogisticStatisticsDayReport> sepData = SeparateUtils.separateData(dayReportList, reportList);
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            sepData.getAddList().forEach(item -> {
                ContextUtils.initEntityCreator(item);
                item.setCreateTime(LocalDateTime.now());
            });
            saveBatch( sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchById(sepData.getUpdateList());
        }
    }

    /**
     * 添加货物统计报表
     * @param dayReport
     * @return
     */
    public Long addLogisticStatisticsDayReport(LogisticStatisticsDayReport dayReport) {
        LogisticStatisticsDayReport dbItem = findLogisticStatisticsDayReport(dayReport);
        if (dbItem != null) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "该物流单号已存在，单号为："+ dbItem.getLogisticsNo());
        }
        boolean save = save(dayReport);
        return save ? dayReport.getId() : 0L;
    }

    /**
     * 更新货物统计报表
     * @param logisticStatisticsDayReport
     */
    public void updateLogisticStatisticsDayReport(LogisticStatisticsDayReport logisticStatisticsDayReport) {
        if (!WrapperClassUtils.biggerThanLong(logisticStatisticsDayReport.getId(), 0)) {
            throw new InvalidParameterException();
        }
        if (logisticStatisticsDayReport.getLoadingWeight() != null && logisticStatisticsDayReport.getStorageWeight() != null) {
            //未入库重量 = 装载重量 - 入库重量
            logisticStatisticsDayReport.setUnStockedWeight(DecimalUtils.subtract(logisticStatisticsDayReport.getLoadingWeight()
                    , logisticStatisticsDayReport.getStorageWeight()));
        }
        if (logisticStatisticsDayReport.getFreightIncomeAmount() != null && logisticStatisticsDayReport.getActualFreightAmount() != null) {
            //未收款金额 = 运费收入 - 实际运费 - 冻品优惠
            logisticStatisticsDayReport.setUnpaidAmount(DecimalUtils.subtract(logisticStatisticsDayReport.getFreightIncomeAmount()
                    , logisticStatisticsDayReport.getActualFreightAmount(), logisticStatisticsDayReport.getFrozenGoodsDiscounts()));
        }
        ContextUtils.initEntityModifier(logisticStatisticsDayReport);
        logisticStatisticsDayReport.setModifiedTime(LocalDateTime.now());
        updateById(logisticStatisticsDayReport);
    }

    /**
     * 根据Id获取货物统计报表
     * @param id
     * @return
     */
    public LogisticStatisticsDayReport findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据Id删除货物统计报表
     * @param id
     */
    public void deleteById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        removeById(id);
    }

    /**
     * 根据id集合查询货物统计报表
     * @param ids
     * @return
     */
    public List<LogisticStatisticsDayReport> listByIds(List<Long> ids) {
        return list(new QueryWrapper<LogisticStatisticsDayReport>().lambda()
                .in(LogisticStatisticsDayReport::getId, ids));
    }

    /**
     * 批量收款
     * @param ids
     * @param paymentTime
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<Long> ids, LocalDateTime paymentTime) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException("id集合不能为空");
        }
        List<LogisticStatisticsDayReport> dayReportList = listByIds(ids);
        if (CollectionUtils.isEmpty(dayReportList)) {
            log.info("货物统计报表未找到， {}", JsonUtils.object2Json(ids));
            return;
        }
        dayReportList.forEach(dayReport -> {
            dayReport.setPaymentTime(paymentTime);
            dayReport.setActualFreightAmount(dayReport.getFreightIncomeAmount());
            dayReport.setUnpaidAmount(DecimalUtils.subtract(dayReport.getFreightIncomeAmount()
                    , dayReport.getActualFreightAmount(), dayReport.getFrozenGoodsDiscounts()));
        });
        updateBatchById(dayReportList);
    }

    /**
     * 根据日期、物流单号查询货物统计
     * @param report
     * @return
     */
    private LogisticStatisticsDayReport findLogisticStatisticsDayReport(LogisticStatisticsDayReport report) {
        List<LogisticStatisticsDayReport> dayReportList = listByLogisticStatisticsDayReport(Collections.singletonList(report));
        return CollectionUtils.isEmpty(dayReportList) ? null : dayReportList.get(0);
    }

    /**
     * 根据日期、物流单号查询货物统计
     * @param list
     * @return
     */
    private List<LogisticStatisticsDayReport> listByLogisticStatisticsDayReport(List<LogisticStatisticsDayReport> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<LogisticStatisticsDayReport> resultList = new ArrayList<>(list.size());
        List<String> logisticsList = list.stream().map(LogisticStatisticsDayReport::getLogisticsNo).collect(Collectors.toList());
        resultList.addAll(getBaseMapper().listByLogisticStatisticsDayReport(logisticsList));
        return resultList;
    }

    /**
     * 货物统计报表分页
     * @param searchVo
     * @return
     */
    public LogisticStatisticsDayReportPageResult<LogisticStatisticsDayReportVo> page(LogisticStatisticsDayReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return LogisticStatisticsDayReportPageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<LogisticStatisticsDayReportVo> page = PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countLogisticStatisticsDayReport(searchVo), () -> getBaseMapper().listLogisticStatisticsDayReport(searchVo));
        LogisticStatisticsDayReportPageResult<LogisticStatisticsDayReportVo> resultPage = new LogisticStatisticsDayReportPageResult<>(page.getPageNo(), page.getPageSize());
        resultPage.setTotalPages(page.getTotalPages());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setDataList(page.getDataList());

        LogisticStatisticsDayReportVo sumVo = getBaseMapper().sumLogisticStatisticsDayReport(searchVo);
        if (sumVo != null) {
            resultPage.setPieces(sumVo.getPieces());
            resultPage.setLoadingWeight(sumVo.getLoadingWeight());
            resultPage.setStorageWeight(sumVo.getStorageWeight());
            resultPage.setUnStockedWeight(sumVo.getUnStockedWeight());
            resultPage.setVolume(sumVo.getVolume());
            resultPage.setFreightIncomeAmount(sumVo.getFreightIncomeAmount());
            resultPage.setActualFreightAmount(sumVo.getActualFreightAmount());
            resultPage.setFrozenGoodsDiscounts(sumVo.getFrozenGoodsDiscounts());
            resultPage.setUnpaidAmount(sumVo.getUnpaidAmount());
        }
        return resultPage;
    }
}
