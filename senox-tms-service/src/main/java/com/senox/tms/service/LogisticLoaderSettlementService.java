package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.domain.LogisticLoaderSettlement;
import com.senox.tms.mapper.LogisticLoaderSettlementMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.utils.LogisticLoaderUtils;
import com.senox.tms.vo.BicycleTotalPageResult;
import com.senox.tms.vo.DictLogisticVo;
import com.senox.tms.vo.LogisticLoaderSettlementSearchVo;
import com.senox.tms.vo.LogisticLoaderSettlementVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@RequiredArgsConstructor
@Service
public class LogisticLoaderSettlementService extends ServiceImpl<LogisticLoaderSettlementMapper, LogisticLoaderSettlement> {
    private final LogisticLoaderIncomeService loaderIncomeService;
    private final DictLogisticService dictLogisticService;


    /**
     * 添加
     *
     * @param settlement 结算
     */
    public boolean add(LogisticLoaderSettlement settlement) {
        return addBatch(Collections.singleton(settlement)) > 0;
    }

    /**
     * 批量添加
     *
     * @param settlements 结算集
     * @return 影响条
     */
    public int addBatch(Collection<LogisticLoaderSettlement> settlements) {
        if (CollectionUtils.isEmpty(settlements)) {
            return 0;
        }
        settlements.forEach(c -> {
            c.setTransportAvg(c.getTransportTotal().divide(new BigDecimal(c.getParticipationNumber()),2,RoundingMode.HALF_EVEN));
            c.setFreightTotalAmount(DecimalUtils.multiple(c.getTransportTotal(),c.getFreightUnitPrice()));
            c.setSubtotalAmount(DecimalUtils.multiple(c.getDrivingHours(),new BigDecimal("20")).add(c.getMealAllowanceAmount()));
            c.setTotalAmount(c.getFreightTotalAmount().add(c.getSortingFee()).add(c.getSubtotalAmount()));
            ContextUtils.initEntityCreator(c);
            ContextUtils.initEntityModifier(c);
        });
        return baseMapper.addBatch(settlements);
    }

    /**
     * 添加结算和添加收益
     * @param settlement 结算
     * @param loaderIds 搬运工id集
     */
    public void addAndIncome(LogisticLoaderSettlement settlement, List<Long> loaderIds) {
        List<DictLogisticVo> loaders = new ArrayList<>();
        if (!CollectionUtils.isEmpty(loaderIds)) {
            loaders = dictLogisticService.listByIds(loaderIds, DictLogisticCategory.LOADER);
        }
        settlement.setParticipationNumber(loaders.size());
        if (add(settlement)) {
            loaderIncomeService.addBatch(LogisticLoaderUtils.builderLoaderIncome(settlement, loaders));
        }
    }

    /**
     * 更新
     *
     * @param settlement 结算
     */
    public boolean update(LogisticLoaderSettlement settlement) {
        return updateBatch(Collections.singleton(settlement)) > 0;
    }

    /**
     * 更新和刷新收益
     *
     * @param settlement 结算
     * @param loaderIds  搬运工id集
     */
    public void updateAndRefreshIncome(LogisticLoaderSettlement settlement, List<Long> loaderIds) {
        List<DictLogisticVo> loaders = new ArrayList<>();
        if (!CollectionUtils.isEmpty(loaderIds)) {
            loaders = dictLogisticService.listByIds(loaderIds, DictLogisticCategory.LOADER);
        }
        settlement.setParticipationNumber(loaders.size());
        if (update(settlement)) {
            loaderIncomeService.deleteBySettlementId(settlement.getId());
            loaderIncomeService.addBatch(LogisticLoaderUtils.builderLoaderIncome(super.getById(settlement.getId()), loaders));
        }
    }

    /**
     * 批量更新
     *
     * @param settlements 结集
     * @return 影响条
     */
    public int updateBatch(Collection<LogisticLoaderSettlement> settlements) {
        if (CollectionUtils.isEmpty(settlements)) {
            return 0;
        }
        settlements.forEach(ContextUtils::initEntityModifier);
        return baseMapper.updateBatch(settlements);
    }

    /**
     * 根据id删除
     *
     * @param id id
     */
    @Transactional
    public void deleteById(Long id) {
        if (deleteByIds(Collections.singletonList(id))) {
            loaderIncomeService.deleteBySettlementId(id);
        }
    }

    /**
     * 根据id集删除
     *
     * @param ids id集
     */
    public boolean deleteByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        return removeByIds(ids);
    }

    /**
     * 根据id获取结算
     *
     * @return 返回获取到的结算
     */
    public LogisticLoaderSettlementVo getById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return baseMapper.getById(id);
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public List<LogisticLoaderSettlementVo> list(LogisticLoaderSettlementSearchVo searchVo) {
        return baseMapper.list(searchVo);
    }

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    public int countList(LogisticLoaderSettlementSearchVo searchVo) {
        return baseMapper.countList(searchVo);
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public BicycleTotalPageResult<LogisticLoaderSettlementVo> listPage(LogisticLoaderSettlementSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicycleTotalPageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<LogisticLoaderSettlementVo> pageResult = PageUtils.commonPageResult(searchVo, () -> countList(searchVo), () -> list(searchVo));
        return new BicycleTotalPageResult<>(pageResult,baseMapper.listTotalAmount(searchVo));
    }
}
