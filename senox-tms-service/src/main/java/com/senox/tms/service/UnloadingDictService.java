package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.domain.UnloadingDict;
import com.senox.tms.mapper.UnloadingDictMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.UnloadingDictSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/12 13:35
 */
@Service
@Slf4j
public class UnloadingDictService extends ServiceImpl<UnloadingDictMapper, UnloadingDict> {

    /**
     * 批量添加字典
     * @param dicts
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchAddDict(List<UnloadingDict> dicts) {
        if (CollectionUtils.isEmpty(dicts)) {
            log.info("【dict batchAdd】 dictList is empty, return");
            return;
        }
        dicts.forEach(dict -> {
            ContextUtils.initEntityCreator(dict);
            ContextUtils.initEntityModifier(dict);
            dict.setCreateTime(LocalDateTime.now());
            dict.setModifiedTime(LocalDateTime.now());
        });
        List<UnloadingDict> addDictList = new ArrayList<>(dicts.size());
        Map<Integer, List<UnloadingDict>> categoryMap = dicts.stream().collect(Collectors.groupingBy(UnloadingDict::getCategory));
        for (Map.Entry<Integer, List<UnloadingDict>> entry : categoryMap.entrySet()) {
            List<String> dictNames = entry.getValue().stream().map(UnloadingDict::getName).collect(Collectors.toList());
            List<UnloadingDict> existsDictList = dictNameListByNames(dictNames, entry.getKey());
            List<UnloadingDict> unloadingDictList = entry.getValue().stream().filter(dict -> !existsDictList.contains(dict)).collect(Collectors.toList());
            addDictList.addAll(unloadingDictList);
        }
        saveBatch(addDictList);
    }

    /**
     * 添加字典
     * @param dict
     */
    public void addDict(UnloadingDict dict) {
        batchAddDict(Collections.singletonList(dict));
    }

    /**
     * 更新字典
     * @param dict
     */
    public void updateDict(UnloadingDict dict) {
        if (!WrapperClassUtils.biggerThanLong(dict.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        List<UnloadingDict> unloadingDictList = dictNameListByNames(Collections.singletonList(dict.getName()), dict.getCategory());
        if (!CollectionUtils.isEmpty(unloadingDictList)) {
            throw new BusinessException("【字典名称存在】名称值为：{}"+ dict.getName());
        }
        ContextUtils.initEntityModifier(dict);
        dict.setModifiedTime(LocalDateTime.now());
        updateById(dict);
    }

    public UnloadingDict findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 删除字典
     * @param dict
     */
    public void deleteDict(UnloadingDict dict) {
        if (!WrapperClassUtils.biggerThanLong(dict.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        UnloadingDict dbDict = getById(dict.getId());
        if (dbDict != null) {
            dict.setName(dbDict.getName().concat("-").concat(dbDict.getId().toString()));
            dict.setDisabled(Boolean.TRUE);
            ContextUtils.initEntityModifier(dict);
            dict.setModifiedTime(LocalDateTime.now());
            updateById(dict);
        }
    }


    /**
     * 装卸字典分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingDict> dictPageResult(UnloadingDictSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countDict(searchVo), () -> getBaseMapper().listDict(searchVo));
    }

    /**
     * 根据字典名称查询
     * @param names
     * @return
     */
    public List<UnloadingDict> dictNameListByNames(List<String> names, Integer category) {
        LambdaQueryWrapper<UnloadingDict> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UnloadingDict::getDisabled, Boolean.FALSE);
        if (!CollectionUtils.isEmpty(names)) {
            wrapper.in(UnloadingDict::getName, names);
        }
        if (category != null) {
            wrapper.eq(UnloadingDict::getCategory, category);
        }
        return list(wrapper);
    }

}
