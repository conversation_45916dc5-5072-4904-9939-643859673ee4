package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.domain.LogisticsFreight;
import com.senox.tms.mapper.LogisticsFreightMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
@RequiredArgsConstructor
@Service
public class LogisticsFreightService extends ServiceImpl<LogisticsFreightMapper, LogisticsFreight> {

    /**
     * 添加
     *
     * @param freight 货运
     */
    public void add(LogisticsFreight freight) {
       addBatch(Collections.singletonList(freight));
    }

    /**
     * 批量添加
     *
     * @param freights 货运列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<LogisticsFreight> freights) {
        if (CollectionUtils.isEmpty(freights)){
            return;
        }
        freights.forEach(f->{
            ContextUtils.initEntityCreator(f);
            ContextUtils.initEntityModifier(f);
            f.setCreateTime(LocalDateTime.now());
            f.setModifiedTime(LocalDateTime.now());
        });
        saveBatch(freights);
    }

    /**
     * 更新
     *
     * @param freight 货运
     */
    public void update(LogisticsFreight freight) {
        if (!WrapperClassUtils.biggerThanLong(freight.getId(), 0)) {
            throw new InvalidParameterException();
        }
        ContextUtils.initEntityModifier(freight);
        freight.setModifiedTime(LocalDateTime.now());
        updateById(freight);
    }

    /**
     * 根据id删除
     *
     * @param id id
     */
    public void deleteById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        removeById(id);
    }

    /**
     * 列表
     *
     * @param searchVo 货运查询
     * @return 返回列表
     */
    public List<LogisticsFreight> list(LogisticsFreightSearchVo searchVo) {
        return baseMapper.list(searchVo);
    }

    /**
     * 列表统计
     *
     * @param searchVo 货运查询
     * @return 返回统计
     */
    public int countList(LogisticsFreightSearchVo searchVo) {
        return baseMapper.countList(searchVo);
    }

    /**
     * 列表分页查询
     *
     * @param searchVo 货运查询
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<LogisticsFreight, LogisticsFreightStatisticsVo> listPage(LogisticsFreightSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<LogisticsFreight> pageResult = PageUtils.commonPageResult(searchVo, () -> countList(searchVo), () -> list(searchVo));
        LogisticsFreightStatisticsVo statistics = baseMapper.listTotalAmount(searchVo);
        return new PageStatisticsResult<>(pageResult,statistics);
    }

    /**
     * 根据id查找
     * @param id id
     * @return 返回查找到的
     */
    public LogisticsFreight findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id,0)) {
            throw new InvalidParameterException();
        }
        return baseMapper.findById(id);
    }
}
