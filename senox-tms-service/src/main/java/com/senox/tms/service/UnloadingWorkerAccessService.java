package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.dm.constant.EmployeeType;
import com.senox.dm.vo.AccessDeviceRightVo;
import com.senox.dm.vo.EmployeeAccessStateVo;
import com.senox.tms.component.AccessControlComponent;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.domain.UnloadingWorkerAccess;
import com.senox.tms.mapper.UnloadingWorkerAccessMapper;
import com.senox.tms.utils.ContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/29 9:37
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UnloadingWorkerAccessService extends ServiceImpl<UnloadingWorkerAccessMapper, UnloadingWorkerAccess> {

    private final AccessControlComponent accessControlComponent;

    /**
     * 添加搬运工设备权限
     * @param worker
     * @param accessList
     */
    @Transactional(rollbackFor = Exception.class)
    public void addWorkerAccess(UnloadingWorker worker, List<UnloadingWorkerAccess> accessList) {
        if (CollectionUtils.isEmpty(accessList)) {
            log.info("【搬运工设备权限】无数据。。。");
        }
        accessList = accessList.stream().distinct().collect(Collectors.toList());
        List<UnloadingWorkerAccess> dbAccessList = accessListByWorkerIds(Collections.singletonList(worker.getId()));
        DataSepDto<UnloadingWorkerAccess> sepDto = SeparateUtils.separateData(dbAccessList, accessList);
        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            sepDto.getAddList().forEach(x -> {
                ContextUtils.initEntityCreator(x);
                ContextUtils.initEntityModifier(x);
                x.setCreateTime(LocalDateTime.now());
                x.setModifiedTime(LocalDateTime.now());
            });
            saveBatch(sepDto.getAddList());
            accessRight(worker, sepDto.getAddList(), Boolean.FALSE);
        }
        if (!CollectionUtils.isEmpty(sepDto.getRemoveList())) {
            removeByIds(sepDto.getRemoveList().stream().map(UnloadingWorkerAccess::getId).collect(Collectors.toList()));
            accessRight(worker, sepDto.getRemoveList(), Boolean.TRUE);
        }
    }

    /**
     * 根据id查询权限
     * @param id
     * @return
     */
    public UnloadingWorkerAccess findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 删除权限
     * @param access
     * @param worker
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAccess(UnloadingWorkerAccess access, UnloadingWorker worker) {
        if (worker == null) {
            log.info("【删除搬运工权限】----- 搬运工未找到。。。");
            return;
        }
        removeById(access.getId());
        accessRight(worker, Collections.singletonList(access), Boolean.TRUE);
    }


    /**
     * 根据搬运工Id查询设备权限
     * @param workerIds
     * @return
     */
    public List<UnloadingWorkerAccess> accessListByWorkerIds(List<Long> workerIds) {
        if (CollectionUtils.isEmpty(workerIds)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<UnloadingWorkerAccess>().lambda().in(UnloadingWorkerAccess::getWorkerId, workerIds));
    }

    /**
     * 根据搬运工标识和设备号查询相关权限
     *
     * @param workerSign
     * @param deviceId
     * @return
     */
    public List<UnloadingWorkerAccess> workerAccessList(String workerSign, Long deviceId) {
        LambdaQueryWrapper<UnloadingWorkerAccess> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isBlank(workerSign) && !WrapperClassUtils.biggerThanLong(deviceId, 0L)) {
            return Collections.emptyList();
        }
        if (!StringUtils.isBlank(workerSign)) {
            wrapper.eq(UnloadingWorkerAccess::getWorkerSign, workerSign);
        }
        if (WrapperClassUtils.biggerThanLong(deviceId, 0L)) {
            wrapper.eq(UnloadingWorkerAccess::getDeviceId, deviceId);
        }
        wrapper.eq(UnloadingWorkerAccess::getDisabled, Boolean.FALSE);
        return list(wrapper);
    }

    /**
     * 更新权限状态
     * @param stateVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkerAccessState(EmployeeAccessStateVo stateVo) {
        if (StringUtils.isBlank(stateVo.getEmployeeNo()) || !WrapperClassUtils.biggerThanLong(stateVo.getDeviceId(), 0L)) {
            throw new InvalidParameterException();
        }
        List<UnloadingWorkerAccess> workerAccessList = workerAccessList(stateVo.getEmployeeNo(), stateVo.getDeviceId());
        if (CollectionUtils.isEmpty(workerAccessList)) {
            return;
        }
        workerAccessList.forEach(workerAccess -> {
            workerAccess.setState(Boolean.TRUE);
            workerAccess.setModifiedTime(LocalDateTime.now());
        });
        updateBatchById(workerAccessList);
    }

    /**
     * 下发设备权限
     * @param worker
     * @param accessList
     * @param isDelete
     */
    public void accessRight(UnloadingWorker worker, List<UnloadingWorkerAccess> accessList, Boolean isDelete) {
        if (worker == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        AccessDeviceRightVo rightVo = new AccessDeviceRightVo();
        rightVo.setDeviceIps(accessList.stream().map(UnloadingWorkerAccess::getDeviceIp).toArray(String[]::new));
        rightVo.setEmployeeNo(worker.getWorkerSign());
        rightVo.setEmployeeName(worker.getName());
        rightVo.setFaceUrl(worker.getFaceUrl());
        rightVo.setEmployeeType(EmployeeType.WORKER.getValue());
        if (BooleanUtils.isTrue(isDelete)) {
            accessControlComponent.deleteAccessRight(rightVo);
        } else {
            accessControlComponent.addAccessRight(rightVo);
        }
    }
}
