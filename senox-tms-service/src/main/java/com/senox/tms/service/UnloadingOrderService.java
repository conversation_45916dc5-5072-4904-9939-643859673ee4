package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.tms.config.AppConfig;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.constant.UnloadingOrderState;
import com.senox.tms.constant.UnloadingOrderWorkerStatus;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.convert.UnloadingOrderConvert;
import com.senox.tms.convert.UnloadingOrderGoodsConvert;
import com.senox.tms.convert.UnloadingOrderWorkersConvert;
import com.senox.tms.domain.UnloadingOrder;
import com.senox.tms.domain.UnloadingOrderGoods;
import com.senox.tms.domain.UnloadingOrderWorkers;
import com.senox.tms.mapper.UnloadingOrderMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.UnloadOrderSearchVo;
import com.senox.tms.vo.UnloadingOrderVo;
import com.senox.tms.vo.UnloadingOrderWorkersVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/14 14:23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UnloadingOrderService extends ServiceImpl<UnloadingOrderMapper, UnloadingOrder> {

    private final AppConfig appConfig;
    private final UnloadingOrderGoodsService orderGoodsService;
    private final UnloadingOrderWorkersService orderWorkersService;
    private final UnloadingWorkerService workerService;
    private final UnloadingOrderWorkersConvert orderWorkersConvert;
    private final UnloadingOrderGoodsConvert orderGoodsConvert;
    private final UnloadingOrderConvert orderConvert;


    /**
     * 保存订单
     * @param order
     * @param goods
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrder(UnloadingOrder order, List<UnloadingOrderGoods> goods) {
        UnloadingOrder dbOrder = null;
        if (WrapperClassUtils.biggerThanLong(order.getId(), 0L)) {
            dbOrder = findById(order.getId());
        }
        //prepare orderNo
        if (dbOrder == null && StringUtils.isBlank(order.getOrderNo())) {
            log.info("【装卸订单】---订单编号开始生成---");
            prepareOrderNo(order);
            ContextUtils.initEntityCreator(order);
            order.setCreateTime(LocalDateTime.now());
        }
        if (UnloadingOrderState.NORMAL.getNumber() == order.getState()) {
            order.setOrderTime(LocalDateTime.now());
        }
        ContextUtils.initEntityModifier(order);
        order.setModifiedTime(LocalDateTime.now());
        boolean result = saveOrUpdate(order);
        if (result) {
            //add goods
            orderGoodsService.saveGoods(order.getId(), goods);
        }
    }

    /**
     * 删除订单
     * @param id
     */
    public void deleteOrder(Long id) {
        UnloadingOrder unloadingOrder = findById(id);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (unloadingOrder.getWorkerStatus() == UnloadingOrderWorkerStatus.TRANSPORTING.getNumber()
                || unloadingOrder.getWorkerStatus() == UnloadingOrderWorkerStatus.DONE.getNumber()) {
            throw new BusinessException("订单已经分配或已完成");
        }
        removeById(id);
    }

    /**
     * 取消订单
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Long id) {
        UnloadingOrder unloadingOrder = findById(id);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (unloadingOrder.getWorkerStatus() != UnloadingOrderWorkerStatus.TRANSPORTING.getNumber()) {
            throw new BusinessException("订单是分配中的才能取消！");
        }
        unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.INIT.getNumber());
        UnloadingOrderVo detail = findDetailById(id);
        if (detail == null || CollectionUtils.isEmpty(detail.getWorkersVoList())) {
            throw new BusinessException("未查询到该订单的分派信息！");
        }
        workerService.updateBatchWorkerStatus(detail.getWorkersVoList().stream().map(UnloadingOrderWorkersVo::getWorkerId).collect(Collectors.toList())
                , UnloadingWorkerStatus.ALREADY_LISTED.getNumber());
        orderWorkersService.saveWorkers(id, Collections.emptyList());
    }

    /**
     * 根据id查询订单
     * @param id
     * @return
     */
    public UnloadingOrder findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据orderNo查询订单
     * @param orderNo
     * @return
     */
    public UnloadingOrder findByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }
        return getOne(new QueryWrapper<UnloadingOrder>().lambda().eq(UnloadingOrder::getOrderNo, orderNo));
    }

    /**
     * 订单列表
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderVo> listOrder(UnloadOrderSearchVo searchVo) {
        return getBaseMapper().listOrder(searchVo);
    }

    /**
     * 订单合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderVo sumOrder(UnloadOrderSearchVo searchVo) {
        return getBaseMapper().sumOrder(searchVo);
    }

    /**
     * 订单分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderVo> pageResult(UnloadOrderSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countOrder(searchVo), () -> getBaseMapper().listOrder(searchVo));
    }

    /**
     * 订单详细分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderVo> pageDetailResult(UnloadOrderSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countOrder(searchVo), () -> {
            List<UnloadingOrderVo> orderVos = getBaseMapper().listOrder(searchVo);
            List<UnloadingOrderWorkers> workersList = orderWorkersService.listByOrderIds(orderVos.stream().map(UnloadingOrderVo::getId).collect(Collectors.toList()));
            List<UnloadingOrderGoods> goodsList = orderGoodsService.listByOrderIds(orderVos.stream().map(UnloadingOrderVo::getId).collect(Collectors.toList()));
            Map<Long, List<UnloadingOrderWorkers>> workersMap = workersList.stream().collect(Collectors.groupingBy(UnloadingOrderWorkers::getOrderId));
            Map<Long, List<UnloadingOrderGoods>> goodsMap = goodsList.stream().collect(Collectors.groupingBy(UnloadingOrderGoods::getOrderId));
            orderVos.forEach(order -> {
                List<UnloadingOrderWorkers> workers = workersMap.get(order.getId());
                List<UnloadingOrderGoods> goods = goodsMap.get(order.getId());
                order.setWorkersVoList(orderWorkersConvert.toVo(workers));
                order.setGoodsVoList(orderGoodsConvert.toVo(goods));
            });
            return orderVos;
        });
    }

    /**
     * 根据id查询订单详细
     * @param id
     * @return
     */
    public UnloadingOrderVo findDetailById(Long id) {
        return getBaseMapper().findDetailById(id);
    }

    /**
     * 批量更新订单应付金额
     * @param orderVos
     */
    public void updateBatchOrderPayoffAmount(List<UnloadingOrderVo> orderVos) {
        List<UnloadingOrder> orders = orderConvert.toDo(orderVos);
        orders.forEach(order -> {
            ContextUtils.initEntityModifier(order);
            order.setModifiedTime(LocalDateTime.now());
        });
        updateBatchById(orders);
    }

    /**
     * 指定搬运工人数
     * @param orderId
     * @param workerNum
     */
    public void appointWorkerNum(Long orderId, Integer workerNum) {
        UnloadingOrder unloadingOrder = findById(orderId);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "装卸订单未找到！");
        }
        unloadingOrder.setWorkerNum(workerNum);
        ContextUtils.initEntityModifier(unloadingOrder);
        unloadingOrder.setModifiedTime(LocalDateTime.now());
        updateById(unloadingOrder);
    }

    /**
     * 分派搬运工
     * @param orderId
     * @param workers
     */
    @Transactional(rollbackFor = Exception.class)
    public void assignWorkers(Long orderId, List<UnloadingOrderWorkers> workers) {
        UnloadingOrder unloadingOrder = findById(orderId);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "装卸订单未找到！");
        }
        unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.TRANSPORTING.getNumber());
        unloadingOrder.setWorkTime(LocalDateTime.now());
        if (CollectionUtils.isEmpty(workers)) {
            unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.INIT.getNumber());
            unloadingOrder.setWorkTime(null);
        }
        unloadingOrder.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(unloadingOrder);
        updateById(unloadingOrder);
        orderWorkersService.saveWorkers(orderId, workers);
        workerService.updateBatchWorkerStatus(workers.stream().map(UnloadingOrderWorkers::getWorkerId).collect(Collectors.toList())
                , UnloadingWorkerStatus.DURING_TRANSPORTATION.getNumber());
    }

    /**
     * 完成订单
     * @param orderId
     * @param amount
     */
    @Transactional(rollbackFor = Exception.class)
    public void finishOrder(Long orderId, BigDecimal amount) {
        UnloadingOrder unloadingOrder = findById(orderId);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        unloadingOrder.setAmount(amount);
        unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.DONE.getNumber());
        unloadingOrder.setFinishTime(LocalDateTime.now());
        unloadingOrder.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(unloadingOrder);
        updateById(unloadingOrder);
        List<UnloadingOrderWorkers> workersList = orderWorkersService.listByOrderId(orderId);
        workersList.forEach(worker ->
            workerService.updateWorkerStatus(worker.getWorkerId(), UnloadingWorkerStatus.NOT_LISTED.getNumber())
        );
    }

    /**
     * 更新订单金额
     * @param orderNo
     * @param amount
     */
    public void updateOrderAmount(String orderNo, BigDecimal amount) {
        UnloadingOrder order = findByOrderNo(orderNo);
        if (order == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "订单未找到！");
        }
        order.setAmount(amount);
        ContextUtils.initEntityModifier(order);
        order.setModifiedTime(LocalDateTime.now());
        updateById(order);
    }

    /**
     * 订单编号初始化
     * @param order
     */
    private void prepareOrderNo(UnloadingOrder order) {
        String prefix = appConfig.getUnloadingOrderNoPrefix().concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        // get serial from cache
        String cacheKey = String.format(TmsConst.Cache.KEY_UNLOADING_ORDER_NO, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, TmsConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxOrderNo(prefix);
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, TmsConst.Cache.TTL_2D);
        }
        String orderNo = prefix.concat(StringUtils.fixLength(String.valueOf(result), appConfig.getUnloadingOrderNoPostfixLength(), appConfig.getFillChar()));
        log.info("装卸订单编号初始化完成...{}", orderNo);
        order.setOrderNo(orderNo);
    }

    /**
     * 获取最大订单序号
     * @param prefix
     * @return
     */
    private Long findMaxOrderNo(String prefix) {
        String maxOrderNo = getBaseMapper().findMaxOrderNo(prefix);
        return (StringUtils.isBlank(maxOrderNo) ? 0L : NumberUtils.parseLong(maxOrderNo.substring(prefix.length()), 0L));
    }
}
