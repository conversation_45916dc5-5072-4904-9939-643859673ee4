package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.component.MerchantComponent;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.constant.BicycleSettingType;
import com.senox.tms.constant.BicycleStatisticsSearchUnit;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.convert.BicycleDeliveryDetailItemConvert;
import com.senox.tms.convert.BicycleDeliveryOrderDetailConvert;
import com.senox.tms.domain.*;
import com.senox.tms.event.BicycleBillSettlementAdhocEvent;
import com.senox.tms.event.BicycleCancelJobEvent;
import com.senox.tms.event.BicyclePayoffDeliveryTimeEvent;
import com.senox.tms.mapper.BicycleDeliveryOrderDetailMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import com.senox.user.vo.MerchantVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.Period;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:52
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BicycleDeliveryOrderDetailService extends ServiceImpl<BicycleDeliveryOrderDetailMapper, BicycleDeliveryOrderDetail> {

    private final BicycleOrderService bicycleOrderService;
    private final RabbitTemplate rabbitTemplate;
    private final ApplicationEventPublisher publisher;
    private final BicycleDeliveryOrderDetailItemService orderDetailItemService;
    private final BicycleDeliveryOrderDetailConvert bicycleDeliveryOrderDetailConvert;
    private final BicycleDeliveryDetailItemConvert detailItemConvert;
    private final BicycleSettingService bicycleSettingService;
    private final MerchantComponent merchantComponent;
    private final BicyclePayoffService bicyclePayoffService;
    private final BicycleSharesService sharesService;




    /**
     * 添加配送单详细
     * @param deliveryOrderSerialNo
     * @param detailVos
     * @param status
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> addBicycleDeliveryOrderDetail(String deliveryOrderSerialNo, List<BicycleDeliveryOrderDetailVo> detailVos, Integer status) {
        List<BicycleDeliveryOrderDetail> orderDetailsList = new ArrayList<>();
        List<BicycleDeliveryOrderDetailItem> orderDetailItemList = new ArrayList<>();
        detailVos.forEach(detailVo -> {
            BicycleDeliveryOrderDetail orderDetail = bicycleDeliveryOrderDetailConvert.toDo(detailVo);
            ContextUtils.initEntityCreator(orderDetail);
            ContextUtils.initEntityModifier(orderDetail);
            orderDetail.setCreateTime(LocalDateTime.now());
            orderDetail.setModifiedTime(LocalDateTime.now());
            orderDetail.setDeliveryOrderSerialNo(deliveryOrderSerialNo);
            if (orderDetail.getStatus() == null) {
                orderDetail.setStatus(status);
            }
            if (StringUtils.isBlank(orderDetail.getDeliveryOrderSerialNoItem())) {
                prepareDeliveryOrderItemSerialNo(orderDetail);
            }
            if (!CollectionUtils.isEmpty(detailVo.getDetailItemVos())) {
                List<BicycleDeliveryOrderDetailItem> detailItems = detailItemConvert.toDoList(detailVo.getDetailItemVos());
                detailItems.forEach(detailItem -> {
                    ContextUtils.initEntityCreator(detailItem);
                    ContextUtils.initEntityModifier(detailItem);
                    detailItem.setCreateTime(LocalDateTime.now());
                    detailItem.setModifiedTime(LocalDateTime.now());
                    detailItem.setDeliveryOrderSerialNo(deliveryOrderSerialNo);
                    detailItem.setDeliveryOrderSerialNoItem(orderDetail.getDeliveryOrderSerialNoItem());
                    detailItem.setOrderSerialNo(orderDetail.getOrderSerialNo());
                    orderDetailItemList.add(detailItem);
                });
            }
            orderDetailsList.add(orderDetail);
        });
        //删除该配送单下所以的配送子详细
        orderDetailItemService.deleteByDeliveryOrderSerialNo(deliveryOrderSerialNo);
        List<BicycleDeliveryOrderDetail> orderDetails = listOrderDetailByDeliveryOrderSerialNo(deliveryOrderSerialNo);
        DataSepDto<BicycleDeliveryOrderDetail> sepData = SeparateUtils.separateData(orderDetails, orderDetailsList);
        log.info("转换后的sepData为 ---- {}", JsonUtils.object2Json(sepData));
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
            log.info("插入的配送子详细数据为: {}", JsonUtils.object2Json(orderDetailItemList));
            orderDetailItemService.addBatch(orderDetailItemList);
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchById(sepData.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(BicycleDeliveryOrderDetail::getId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(orderDetailsList) && !CollectionUtils.isEmpty(orderDetails)) {
            removeByIds(orderDetails.stream().map(BicycleDeliveryOrderDetail::getId).collect(Collectors.toList()));
            List<String> orderSerialNoList = orderDetails.stream().map(BicycleDeliveryOrderDetail::getOrderSerialNo).distinct().collect(Collectors.toList());
            //取消订单，删除配送单详细之后，再根据订单号添加
            saveDeliveryOrderDetail(deliveryOrderSerialNo, orderSerialNoList);
        }
        return listOrderDetailByDeliveryOrderSerialNo(deliveryOrderSerialNo).stream().map(BicycleDeliveryOrderDetail::getOrderSerialNo).distinct().collect(Collectors.toList());
    }

    /**
     * 配送订单详细列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleDeliveryOrderDetailVo> listDeliveryDetail(BicycleDeliverySearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countDeliveryDetail(searchVo), () -> getBaseMapper().listDeliveryDetail(searchVo));
    }

    /**
     * 根据订单编号查询配送订单详细信息
     * @param orderSerialNo
     * @return
     */
    public List<BicycleDeliveryDetailInfoVo> deliveryDetailInfoByOrderSerialNo(String orderSerialNo) {
        List<BicycleDeliveryDetailInfoVo> infoVos = getBaseMapper().deliveryDetailInfoByOrderSerialNo(orderSerialNo);
        if (CollectionUtils.isEmpty(infoVos)) {
            return Collections.emptyList();
        }
        List<BicycleDeliveryOrderDetailItemVo> detailItemVoList = detailItemConvert.toVoList(orderDetailItemService.listByOrderSerialNo(orderSerialNo));
        if (CollectionUtils.isEmpty(detailItemVoList)) {
            return infoVos;
        }
        Map<String, List<BicycleDeliveryOrderDetailItemVo>> listMap = detailItemVoList.stream().collect(Collectors.groupingBy(BicycleDeliveryOrderDetailItemVo::getDeliveryOrderSerialNoItem));
        infoVos.forEach(x -> {
            List<BicycleDeliveryOrderDetailItemVo> detailItemVos = listMap.get(x.getDeliveryOrderSerialNoItem());
            x.setDetailItemVos(detailItemVos);
        });
        return infoVos;
    }

    /**
     * 添加配送单详细
     * @param deliveryOrderSerialNo
     * @param orderSerialNoList
     */
    private void saveDeliveryOrderDetail(String deliveryOrderSerialNo, List<String> orderSerialNoList) {
        //多个订单合并才操作
        if (!CollectionUtils.isEmpty(orderSerialNoList) && orderSerialNoList.size() > 1) {
            List<BicycleDeliveryOrderDetail> insertOrderDetailList = new ArrayList<>(orderSerialNoList.size());
            orderSerialNoList.forEach(orderSerialNo-> {
                BicycleDeliveryOrderDetail orderDetail = new BicycleDeliveryOrderDetail();
                orderDetail.setOrderSerialNo(orderSerialNo);
                orderDetail.setDeliveryOrderSerialNo(deliveryOrderSerialNo);
                orderDetail.setCreateTime(LocalDateTime.now());
                orderDetail.setModifiedTime(LocalDateTime.now());
                orderDetail.setStatus(BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber());
                prepareDeliveryOrderItemSerialNo(orderDetail);
                ContextUtils.initEntityCreator(orderDetail);
                ContextUtils.initEntityModifier(orderDetail);
                insertOrderDetailList.add(orderDetail);
            });
            saveBatch(insertOrderDetailList);
        }
    }

    /**
     * 子配送单流水号初始化
     * @param detail
     */
    private void prepareDeliveryOrderItemSerialNo(BicycleDeliveryOrderDetail detail) {
        String prefix = detail.getDeliveryOrderSerialNo().substring(0, 11);
        // get serial from cache
        String cacheKey = String.format(TmsConst.Cache.KEY_BICYCLE_DELIVERY_ORDER_ITEM_SERIAL, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, TmsConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxDeliveryOrderItemNoSerial(prefix);
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, TmsConst.Cache.TTL_2D);
        }
        String serial = detail.getDeliveryOrderSerialNo().concat("-").concat(result.toString());
        log.info("子配送单流水号号初始化完成...{}", serial);
        detail.setDeliveryOrderSerialNoItem(serial);
    }

    /**
     * 获取最大子配送单序号
     *
     * @param prefix
     * @return
     */
    private Long findMaxDeliveryOrderItemNoSerial(String prefix) {
        String maxOrderNo = getBaseMapper().findMaxDeliveryOrderItemNo(prefix);
        return (StringUtils.isBlank(maxOrderNo) ? 0L : NumberUtils.parseLong(maxOrderNo.substring(prefix.length()), 0L));
    }

    /**
     * 更新配送详细单状态
     * @param orderDetails
     * @param status
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(List<BicycleDeliveryOrderDetail> orderDetails, Integer status) {
        orderDetails.forEach(orderDetail -> {
            orderDetail.setStatus(status);
            ContextUtils.initEntityModifier(orderDetail);
            orderDetail.setModifiedTime(LocalDateTime.now());
        });
        updateBatchById(orderDetails);
        if (status == BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber()) {
            //如果为未分配状态则删除配送任务
            publisher.publishEvent(new BicycleCancelJobEvent(this, orderDetails));
        }
    }

    /**
     * 根据id获取配送详细单及任务信息
     * @param id
     * @return
     */
    public BicycleDeliveryOrderDetailVo findDeliveryOrderDetailById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleDeliveryOrderDetailVo detailVo = getBaseMapper().findDeliveryOrderDetailById(id);
        return sortDeliveryOrderJob(detailVo);
    }

    /**
     * 根据id查询配送单详细
     * @param id
     * @return
     */
    public BicycleDeliveryOrderDetail findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据配送单流水号获取配送详细单
     * @param deliveryOrderSerialNo
     * @return
     */
    @Deprecated
    public BicycleDeliveryOrderDetail findByDeliveryOrderSerialNo(String deliveryOrderSerialNo) {
        if (StringUtils.isBlank(deliveryOrderSerialNo)) {
            return null;
        }
        return getOne(new QueryWrapper<BicycleDeliveryOrderDetail>().lambda()
                .eq(BicycleDeliveryOrderDetail::getDeliveryOrderSerialNo, deliveryOrderSerialNo));
    }

    /**
     * 根据订单流水号获取配送详细单及任务信息
     * @param orderSerialNo
     * @return
     */
    public List<BicycleDeliveryOrderDetailVo> findDeliveryOrderDetailByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return Collections.emptyList();
        }
        List<BicycleDeliveryOrderDetailVo> detailVos = getBaseMapper().findDeliveryOrderDetailByOrderSerialNo(orderSerialNo);
        detailVos.forEach(this::sortDeliveryOrderJob);
        return detailVos;
    }

    /**
     * 根据订单流水号获取配送详细单
     * @param orderSerialNo
     * @return
     */
    public List<BicycleDeliveryOrderDetail> findByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return Collections.emptyList();
        }
        return findByOrderSerialNoList(Collections.singletonList(orderSerialNo));
    }

    /**
     * 根据订单流水号集合获取配送详细单
     * @param orderSerialNoList
     * @return
     */
    public List<BicycleDeliveryOrderDetail> findByOrderSerialNoList(List<String> orderSerialNoList) {
        if (CollectionUtils.isEmpty(orderSerialNoList)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<BicycleDeliveryOrderDetail>().lambda()
                .in(BicycleDeliveryOrderDetail::getOrderSerialNo, orderSerialNoList));
    }

    /**
     * 配送单任务排序
     * @param detailVo
     * @return
     */
    public BicycleDeliveryOrderDetailVo sortDeliveryOrderJob(BicycleDeliveryOrderDetailVo detailVo) {
        if (detailVo == null || CollectionUtils.isEmpty(detailVo.getJobVoList())) {
            return detailVo;
        }
        detailVo.setJobVoList(detailVo.getJobVoList().stream().sorted(Comparator.comparing(BicycleDeliveryOrderJobVo::getStatus))
                .collect(Collectors.toList()));
        return detailVo;
    }

    /**
     * 查询骑手当日当月统计
     * @param riderId
     * @param startTime
     * @param endTime
     * @return
     */
    public BicycleRiderCountVo riderCountByRiderId(Long riderId, LocalDateTime startTime, LocalDateTime endTime) {
        if (!WrapperClassUtils.biggerThanLong(riderId, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleRiderCountVo riderCountVo = getBaseMapper().riderCountByRiderId(riderId, startTime, endTime);
        if (riderCountVo == null) {
            riderCountVo = new BicycleRiderCountVo();
            riderCountVo.setTodayCount(0);
            riderCountVo.setTodayReferralIncome(BigDecimal.ZERO);
            riderCountVo.setTodayReferralCount(0);
            riderCountVo.setTodayDeliveryIncome(BigDecimal.ZERO);
            riderCountVo.setTodayPieces(BigDecimal.ZERO);
            riderCountVo.setTodayIncome(BigDecimal.ZERO);
        }
        return riderCountVo;
    }

    /**
     * 查询骑手当日当月统计列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderCountVo> riderCountList(BicycleRiderCountSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().riderCount(searchVo), () -> {
            List<BicycleRiderCountVo> list = getBaseMapper().riderCountList(searchVo);
            list.forEach(l -> l.setAvgDeliveryTimeDescribe(DateUtils.getDurationDescription(l.getAvgDeliveryTime())));
            return list;
        });
    }

    /**
     * 骑手当日统计合计
     * @param searchVo
     * @return
     */
    public BicycleRiderCountVo sumRiderCount(BicycleRiderCountSearchVo searchVo) {
        return getBaseMapper().sumRiderCount(searchVo);
    }

    /**
     * 骑手配送历史统计列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderCountVo> riderHistoryCountList(BicycleRiderCountSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().riderHistoryCount(searchVo), () -> {
            List<BicycleRiderCountVo> list = getBaseMapper().riderHistoryCountList(searchVo);
            list.forEach(d -> d.setAvgDeliveryTimeDescribe(DateUtils.getDurationDescription(d.getAvgDeliveryTime())));
            return list;
        });
    }

    /**
     * 骑手历史配送统计合计
     * @param searchVo
     * @return
     */
    public BicycleRiderCountVo sumRiderHistoryCount(BicycleRiderCountSearchVo searchVo) {
        BicycleRiderCountVo riderCountVo = getBaseMapper().sumRiderHistoryCount(searchVo);
        if (riderCountVo != null) {
            riderCountVo.setAvgDeliveryTimeDescribe(DateUtils.getDurationDescription(riderCountVo.getAvgDeliveryTime()));
        }
        return riderCountVo;
    }

    /**
     * 当天最佳骑手
     * @param startTime
     * @param endTime
     * @return
     */
    public List<BicycleDayBestRiderVo> listDayBestRider(LocalDateTime startTime, LocalDateTime endTime) {
        return getBaseMapper().listDayBestRider(startTime, endTime);
    }

    /**
     * 订单统计
     * @param searchVo
     * @return
     */
    public List<BicycleOrderCountVo> listOrderCount(BicycleStatisticsSearchVo searchVo) {
        if (Objects.equals(BicycleStatisticsSearchUnit.DAY.getNumber(), searchVo.getSearchUnit())) {
            buildStatisticsSearchUnitDay(searchVo);
        } else if (Objects.equals(BicycleStatisticsSearchUnit.MONTH.getNumber(), searchVo.getSearchUnit())) {
            buildStatisticsSearchUnitMonth(searchVo);
        } else if (Objects.equals(BicycleStatisticsSearchUnit.YEAR.getNumber(), searchVo.getSearchUnit())) {
            buildStatisticsSearchUnitYear(searchVo);
        } else {
            throw new BusinessException("搜索单位有误！");
        }
        //处理中和处理完成的订单
        List<BicycleOrderCountVo> doingAndDoneCountVos = getBaseMapper().listOrderDoingAndDoneCount(searchVo);
        //未处理的订单
        List<BicycleOrderCountVo> unDoCountVos = getBaseMapper().listOrderUnDoCount(searchVo);

        //以时间为key组成map
        Map<LocalDateTime, BicycleOrderCountVo> unDoCountVosMap = new HashMap<>();
        for (BicycleOrderCountVo unDoCountVo : unDoCountVos) {
            unDoCountVosMap.put(unDoCountVo.getHours(), unDoCountVo);
        }
        //未处理、处理中、处理完成的订单合计
        List<BicycleOrderCountVo> countVos = new ArrayList<>(doingAndDoneCountVos.size());
        for (BicycleOrderCountVo doingAndDoneCountVo : doingAndDoneCountVos) {
            BicycleOrderCountVo matchingUnDoCountVo = unDoCountVosMap.get(doingAndDoneCountVo.getHours());
            if (matchingUnDoCountVo != null) {
                //在相同时间段的处理中和处理完成订单中设置为处理的订单
                doingAndDoneCountVo.setUndoCount(matchingUnDoCountVo.getUndoCount());
                doingAndDoneCountVo.setPieces(DecimalUtils.add(doingAndDoneCountVo.getPieces(), matchingUnDoCountVo.getPieces()));
                countVos.add(doingAndDoneCountVo);
            }
        }
        return countVos;
    }

    private void buildStatisticsSearchUnitDay(BicycleStatisticsSearchVo searchVo) {
        searchVo.setSearchType("HOUR");
        searchVo.setSearchTypeFormat("%Y-%m-%d %H:00:00");
        StringBuilder stringBuilder = new StringBuilder("SELECT 0 AS number");
        long hours = Duration.between(searchVo.getStartTime(), searchVo.getEndTime()).toHours();
        for (long l = 1; l < hours; l++) {
            stringBuilder.append(" UNION ALL SELECT ").append(l);
        }
        searchVo.setSearchResultFormat(stringBuilder.toString());
    }

    private void buildStatisticsSearchUnitMonth(BicycleStatisticsSearchVo searchVo) {
        searchVo.setSearchType("DAY");
        searchVo.setSearchTypeFormat("%Y-%m-%d");
        StringBuilder stringBuilder = new StringBuilder("SELECT 0 AS number");
        int days = Period.between(searchVo.getStartTime().toLocalDate(), searchVo.getEndTime().toLocalDate()).getDays();
        for (int i = 0; i < days; i++) {
            stringBuilder.append(" UNION ALL SELECT ").append(i + 1);
        }
        searchVo.setSearchResultFormat(stringBuilder.toString());
    }

    private void buildStatisticsSearchUnitYear(BicycleStatisticsSearchVo searchVo) {
        searchVo.setSearchType("MONTH");
        searchVo.setSearchTypeFormat("%Y-%m-01");
        StringBuilder stringBuilder = new StringBuilder("SELECT 0 AS number");
        long months =  ChronoUnit.MONTHS.between(YearMonth.from(searchVo.getStartTime()), YearMonth.from(searchVo.getEndTime()));
        for (long l = 0; l < months; l++) {
            stringBuilder.append(" UNION ALL SELECT ").append(l + 1);
        }
        searchVo.setSearchResultFormat(stringBuilder.toString());
    }

    /**
     * 根据配送单流水号查询配送单详细
     * @param deliveryOrderSerialNo
     * @return
     */
    public List<BicycleDeliveryOrderDetail> listOrderDetailByDeliveryOrderSerialNo(String deliveryOrderSerialNo) {
        if (StringUtils.isBlank(deliveryOrderSerialNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BicycleDeliveryOrderDetail> wrapper = new QueryWrapper<BicycleDeliveryOrderDetail>().lambda();
        wrapper.eq(BicycleDeliveryOrderDetail::getDeliveryOrderSerialNo, deliveryOrderSerialNo);
        return list(wrapper);
    }

    /**
     * 根据订单流水号集合查询配送单详细
     * @param orderSerialNoList
     * @return
     */
    public List<BicycleDeliveryOrderDetail> orderDetailByOrderSerialNo(List<String> orderSerialNoList) {
        if (CollectionUtils.isEmpty(orderSerialNoList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BicycleDeliveryOrderDetail> wrapper = new QueryWrapper<BicycleDeliveryOrderDetail>().lambda();
        wrapper.in(BicycleDeliveryOrderDetail::getOrderSerialNo, orderSerialNoList);
        return list(wrapper);
    }

    /**
     * 根据配送单流水号子单查询配送单详细
     * @param deliveryOrderSerialNoItem
     * @return
     */
    public BicycleDeliveryOrderDetail orderDetailByItemVo(String deliveryOrderSerialNoItem) {
        if (StringUtils.isBlank(deliveryOrderSerialNoItem)) {
            return null;
        }
        LambdaQueryWrapper<BicycleDeliveryOrderDetail> wrapper = new QueryWrapper<BicycleDeliveryOrderDetail>().lambda();
        wrapper.in(BicycleDeliveryOrderDetail::getDeliveryOrderSerialNoItem, deliveryOrderSerialNoItem);
        return getOne(wrapper);
    }

    /**
     * 查询骑手未处理完的配送单
     * @param riderId
     * @return
     */
    public Integer undoDeliveryOrderCount(Long riderId) {
        if (!WrapperClassUtils.biggerThanLong(riderId, 0L)) {
            throw new InvalidParameterException();
        }
        return getBaseMapper().undoDeliveryOrderCount(riderId);
    }

    /**
     * 根据订单流水号删除配送订单
     * @param orderSerialNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteDeliveryOrderDetailByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            throw new InvalidParameterException();
        }
        LambdaQueryWrapper<BicycleDeliveryOrderDetail> wrapper = new QueryWrapper<BicycleDeliveryOrderDetail>().lambda();
        wrapper.eq(BicycleDeliveryOrderDetail::getOrderSerialNo, orderSerialNo);
        remove(wrapper);
    }

    /**
     * 根据配送单流水号删除配送订单
     * @param deliveryOrderSerialNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteDeliveryOrderDetailByDeliveryOrderSerialNo(String deliveryOrderSerialNo) {
        if (StringUtils.isBlank(deliveryOrderSerialNo)) {
            throw new InvalidParameterException();
        }
        LambdaQueryWrapper<BicycleDeliveryOrderDetail> wrapper = new QueryWrapper<BicycleDeliveryOrderDetail>().lambda();
        wrapper.eq(BicycleDeliveryOrderDetail::getDeliveryOrderSerialNo, deliveryOrderSerialNo);
        remove(wrapper);
    }

    /**
     * 更新配送单详细状态
     * @param id
     * @param status
     * @param rating
     * @param orderSerialNo
     * @param point 地点经纬度
     */
    public void updateDeliveryOrderDetailStatus(Long id, BicycleDeliveryOrderStatus status, Integer rating, String orderSerialNo, String point) {
        log.info("【更新单个配送单任务状态】订单号为：{}， 配送单任务id：{}", orderSerialNo, id);
        if (!WrapperClassUtils.biggerThanLong(id, 0L) || status == null) {
            throw new InvalidParameterException();
        }
        BicycleDeliveryOrderDetail orderDetail = new BicycleDeliveryOrderDetail();
        orderDetail.setId(id);
        orderDetail.setStatus(status.getNumber());
        if (BicycleDeliveryOrderStatus.ARRIVING_PICKUP_POINT == status) {
            //设置揽货点经纬度
            orderDetail.setPickingPoint(point);
        } else if (BicycleDeliveryOrderStatus.SEND == status) {
            //设置揽货时间
            orderDetail.setPickingTime(LocalDateTime.now());
            //设置配送时间
            orderDetail.setSendTime(LocalDateTime.now());
        } else if (BicycleDeliveryOrderStatus.ARRIVED_DELIVERY_POINT == status) {
            //设置送货点经纬度
            orderDetail.setDeliveryPoint(point);
        }
        else if (BicycleDeliveryOrderStatus.SEND_COMPLETED == status) {
            log.info("【配送单任务完成。。。。】");
            orderSendCompleted(id, orderSerialNo, orderDetail);
        } else if (BicycleDeliveryOrderStatus.CONFIRM_COMPLETION == status) {
            //设置完成时间
            orderDetail.setFinishTime(LocalDateTime.now());
            orderDetail.setRating(rating);
        }
        orderDetail.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(orderDetail);
        updateById(orderDetail);
    }

    /**
     * 配送完成
     * @param id
     * @param orderSerialNo
     * @param orderDetail
     */
    private void orderSendCompleted(Long id, String orderSerialNo, BicycleDeliveryOrderDetail orderDetail) {
        //设置收货时间
        orderDetail.setReceivingTime(LocalDateTime.now());
        List<BicycleDeliveryOrderDetail> orderDetailList = findByOrderSerialNo(orderSerialNo);
        String deliveryOrderSerialNo = orderDetailList.get(0).getDeliveryOrderSerialNo();
        //计算时效
        List<BicyclePayoff> payoffs = calculateOrderRiderDeliveryTimeByOrderDetail(orderDetailList,orderDetail.getReceivingTime());
        publisher.publishEvent(new BicyclePayoffDeliveryTimeEvent(this,payoffs));
        //排除当前单
        orderDetailList = orderDetailList.stream().filter(x-> !Objects.equals(x.getId(), id)).collect(Collectors.toList());
        log.info("【订单完成】，结果为:{}", JsonUtils.object2Json(orderDetailList));
        if (CollectionUtils.isEmpty(orderDetailList) || orderDetailList.stream().allMatch(x -> x.getStatus() >= BicycleDeliveryOrderStatus.SEND_COMPLETED.getNumber())) {
            log.info("【配送单任务状态一致，更新主配送单状态。。。。】");
            //如果订单不为拆分单或者拆分单的状态也送达
            BicycleOrder order = bicycleOrderService.findBySerialNo(orderSerialNo);
            publisher.publishEvent(new BicycleBillSettlementAdhocEvent(this,order));
            //配送完成发送消息提示客户
            if (order != null && !StringUtils.isBlank(order.getCreateOpenid())) {
                //发送消息
                sendMessage(order);
                int countOrder = bicycleOrderService.countOrder(order.getSenderId());
                BicycleSetting setting = bicycleSettingService.findByAlias(BicycleSettingType.ORDER_COMPLETE_REFERRAL_FEE.getAlias());
                if (setting != null && BooleanUtils.isTrue(setting.getEnable()) && countOrder == 1) {
                    //商户第一次下单，骑手推荐分佣
                    MerchantVo merchantVo = merchantComponent.findById(order.getSenderId());
                    BicyclePayoff payoff = bicyclePayoffService.buildPayoff(LocalDateTime.now(), order.getOrderSerialNo(), deliveryOrderSerialNo, merchantVo.getReferralCode(), BigDecimal.ZERO, sharesService.getCurrentEffectiveShares().getReferralAmount(), BigDecimal.ZERO, true);
                    if (payoff != null) {
                        bicyclePayoffService.save(payoff);
                    }
                }
            }
        }
    }

    /**
     * 计算应付者时效
     * @param orderDetailList 订单详情集
     * @return 返回计算好的应付者集
     */
    private List<BicyclePayoff> calculateOrderRiderDeliveryTimeByOrderDetail(List<BicycleDeliveryOrderDetail> orderDetailList,LocalDateTime receivingTime) {
        if (CollectionUtils.isEmpty(orderDetailList)) {
            return Collections.emptyList();
        }
        List<BicyclePayoff> payoffs = new ArrayList<>();
        orderDetailList.forEach(d -> {
            int deliveryTime = d.getSendTime() == null ? 0 : (int) DateUtils.getDurationBetween(d.getSendTime(), receivingTime, ChronoUnit.SECONDS);
            BicyclePayoff payoff = new BicyclePayoff();
            payoff.setRiderId(d.getRiderId());
            payoff.setDeliveryOrderSerialNo(d.getDeliveryOrderSerialNo());
            payoff.setOrderSerialNo(d.getOrderSerialNo());
            payoff.setDeliveryTime(deliveryTime);
            payoffs.add(payoff);
        });
        return payoffs;
    }

    /**
     * 构建配送单详细
     * @param orderSerialNoList
     * @return
     */
    public List<BicycleDeliveryOrderDetailVo> buildDeliveryOrderDetailList(List<String> orderSerialNoList) {
        List<BicycleDeliveryOrderDetailVo> detailList = new ArrayList<>(orderSerialNoList.size());
        for (String serialNo : orderSerialNoList) {
            BicycleDeliveryOrderDetailVo orderDetail = new BicycleDeliveryOrderDetailVo();
            orderDetail.setOrderSerialNo(serialNo);
            //未分配状态
            orderDetail.setStatus(BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber());
            detailList.add(orderDetail);
        }
        return detailList;
    }

    /**
     * 根据状态查询配送单详细
     * @param status
     * @return
     */
    public List<BicycleDeliveryOrderDetail> listDeliveryOrderDetailByStatus(Integer status) {
        LambdaQueryWrapper<BicycleDeliveryOrderDetail> wrapper = new QueryWrapper<BicycleDeliveryOrderDetail>().lambda();
        wrapper.in(BicycleDeliveryOrderDetail::getStatus, status);
        return list(wrapper);
    }

    /**
     * 发送消息
     * @param order
     */
    private void sendMessage(BicycleOrder order) {
        List<BicycleDeliveryOrderDetailVo> orderDetailVos = findDeliveryOrderDetailByOrderSerialNo(order.getOrderSerialNo());
        BicycleDeliveryOrderDetailVo detailVo = orderDetailVos.get(0);
        BicycleOrderCompleteMessageVo messageVo = new BicycleOrderCompleteMessageVo();
        messageVo.setOrderSerialNo(order.getOrderSerialNo());
        messageVo.setRiderName(detailVo.getRiderName());
        messageVo.setRiderContact(detailVo.getRiderContact());
        messageVo.setSender(order.getSender());
        messageVo.setCreateOpenid(order.getCreateOpenid());
        messageVo.setCompleteTime(LocalDateTime.now());
        rabbitTemplate.convertAndSend(TmsConst.MQ.MQ_TMS_AC_ORDER_COMPLETE, messageVo);
        log.info("发送订单完成消息到消息队列 {}", JsonUtils.object2Json(messageVo));
    }
}
