package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.BicycleFee;
import com.senox.tms.domain.BicycleBill;
import com.senox.tms.domain.BicycleBillDetail;
import com.senox.tms.mapper.BicycleBillMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicycleBillSearchVo;
import com.senox.tms.vo.BicycleBillVo;
import com.senox.tms.vo.BicycleDeliveryOrderDetailVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 10:13
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BicycleBillService extends ServiceImpl<BicycleBillMapper, BicycleBill> {

    private final BicycleBillDetailService bicycleBillDetailService;

    /**
     * 添加配送应收账单
     * @param detailVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void  addBicycleDeliveryBill(BicycleDeliveryOrderDetailVo detailVo, LocalDateTime orderTime) {
        if (detailVo == null) {
            return;
        }
        BicycleBillVo bicycleBill = findByOrderSerialNo(detailVo.getOrderSerialNo());
        BicycleBill bill = buildBillByDeliveryOrderDetail(detailVo, orderTime);
        log.info("生产的应收账单为----:{}", JsonUtils.object2Json(bill));
        if (bicycleBill != null) {
            //已存在应收账单 则修改
            bill.setId(bicycleBill.getId());
            ContextUtils.initEntityModifier(bill);
            bill.setModifiedTime(LocalDateTime.now());
        }
        List<BicycleBillDetail> billDetails = buildListBillDetailListByDeliveryOrderDetail(detailVo);
        boolean result = saveOrUpdate(bill);
        if (result) {
            bicycleBillDetailService.saveBillDetail(billDetails, bill.getId());
        }
    }

    /**
     * 批量更新账单
     *
     * @param bills 账单集
     */
    public void updateBatch(Collection<BicycleBill> bills) {
        bills.forEach(b -> {
            ContextUtils.initEntityModifier(b);
            b.setModifiedTime(LocalDateTime.now());
        });
        super.updateBatchById(bills);
    }

    /**
     * 根据id查询应收账单
     * @param id
     * @return
     */
    public BicycleBill findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        return getById(id);
    }

    /**
     * 根据id获取应收账单详情
     * @param id
     * @return
     */
    public BicycleBillVo findDetailById(Long id) {
        List<BicycleBillVo> resultList = listDetailByIds(Collections.singletonList(id));
        return CollectionUtils.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * 根据id列表获取应收账单详情
     * @param ids
     * @return
     */
    public List<BicycleBillVo> listDetailByIds(List<Long> ids) {
        return CollectionUtils.isEmpty(ids) ? Collections.emptyList() : getBaseMapper().listByIds(ids);
    }

    /**
     * 应收账单合计
     * @param searchVo
     * @return
     */
    public BicycleBillVo sumBill(BicycleBillSearchVo searchVo) {
        return getBaseMapper().sumBill(searchVo);
    }

    /**
     * 应收账单列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleBillVo> listBillPage(BicycleBillSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countBill(searchVo), () -> getBaseMapper().listBill(searchVo));
    }

    /**
     * 应收账单列表
     *
     * @param searchVo 查询参数
     * @return 返回查询到的应收账单列表
     */
    public List<BicycleBillVo> listBill(BicycleBillSearchVo searchVo) {
        searchVo.setPage(false);
        return getBaseMapper().listBill(searchVo);
    }

    /**
     * 订单应收账单列表
     * @param searchVo
     * @return
     */
    public List<BicycleBillVo> listOrderBill(BicycleBillSearchVo searchVo) {
        return getBaseMapper().listOrderBill(searchVo);
    }

    /**
     * 根据配送单详细生成账单
     * @param orderDetailVo
     * @return
     */
    private BicycleBill buildBillByDeliveryOrderDetail(BicycleDeliveryOrderDetailVo orderDetailVo,LocalDateTime orderTime) {
        BicycleBill bill = new BicycleBill();
        bill.setBillDate(orderTime.toLocalDate());
        bill.setBillYear(orderTime.getYear());
        bill.setBillMonth(orderTime.getMonthValue());
        bill.setMerchantId(orderDetailVo.getSenderId());
        bill.setAmount(orderDetailVo.getTotalCharge());
        bill.setDeliveryOrderSerialNo(orderDetailVo.getDeliveryOrderSerialNo());
        bill.setOrderSerialNo(orderDetailVo.getOrderSerialNo());
        ContextUtils.initEntityModifier(bill);
        ContextUtils.initEntityModifier(bill);
        bill.setCreateTime(orderTime);
        bill.setModifiedTime(orderTime);
        return bill;
    }

    /**
     * 根据配送单详细生成账单详细
     *
     * @param orderDetailVo
     * @return
     */
    private List<BicycleBillDetail> buildListBillDetailListByDeliveryOrderDetail(BicycleDeliveryOrderDetailVo orderDetailVo) {
        List<BicycleBillDetail> bicycleBillDetails = new ArrayList<>(BicycleFee.values().length);
        BicycleBillDetail billDetail = null;
        for (BicycleFee value : BicycleFee.values()) {
            if (value == BicycleFee.DELIVERY) {
                billDetail = new BicycleBillDetail();
                billDetail.setFeeId(BicycleFee.DELIVERY.getFeeId());
                billDetail.setFeeName(BicycleFee.DELIVERY.getName());
                billDetail.setAmount(orderDetailVo.getDeliveryCharge());
                bicycleBillDetails.add(billDetail);
            } else if (value == BicycleFee.OTHER) {
                billDetail = new BicycleBillDetail();
                billDetail.setFeeId(BicycleFee.OTHER.getFeeId());
                billDetail.setFeeName(BicycleFee.OTHER.getName());
                billDetail.setAmount(orderDetailVo.getOtherCharge());
                bicycleBillDetails.add(billDetail);
            } else if (value == BicycleFee.BICYCLE_HANDLING) {
                billDetail = new BicycleBillDetail();
                billDetail.setFeeId(BicycleFee.BICYCLE_HANDLING.getFeeId());
                billDetail.setFeeName(BicycleFee.BICYCLE_HANDLING.getName());
                billDetail.setAmount(orderDetailVo.getHandlingCharge());
                bicycleBillDetails.add(billDetail);
            } else if (value == BicycleFee.BICYCLE_UPSTAIRS) {
                billDetail = new BicycleBillDetail();
                billDetail.setFeeId(BicycleFee.BICYCLE_UPSTAIRS.getFeeId());
                billDetail.setFeeName(BicycleFee.BICYCLE_UPSTAIRS.getName());
                billDetail.setAmount(orderDetailVo.getUpstairsCharge());
                bicycleBillDetails.add(billDetail);
            }
        }
        return bicycleBillDetails;
    }

    /**
     * 根据订单流水号查询应收账单
     * @param orderSerialNo
     * @return
     */
    public BicycleBillVo findByOrderSerialNo(String orderSerialNo) {
        return findByOrderSerialNos(Collections.singletonList(orderSerialNo)).stream().findFirst().orElse(null);
    }

    /**
     * 根据订单流水号集查询应收账单列表
     *
     * @param orderSerialNos 订单流水号集
     * @return 返回查询到的应收账单列表
     */
    public List<BicycleBillVo> findByOrderSerialNos(List<String> orderSerialNos) {
        if (CollectionUtils.isEmpty(orderSerialNos)) {
            throw new InvalidParameterException("订单流水号为空");
        }
        return baseMapper.findByOrderSerialNos(orderSerialNos);
    }


    /**
     * 根据订单流水号删除应收账单
     * @param orderSerialNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBicycleBillByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            throw new InvalidParameterException("订单流水号为空");
        }
        BicycleBillVo bicycleBill = findByOrderSerialNo(orderSerialNo);
        if (bicycleBill == null) {
            log.info("【应收账单】记录不存在，{} 对应的应收账单不存在", orderSerialNo);
            return;
        }
        boolean remove = removeById(bicycleBill.getId());
        if (remove) {
            bicycleBillDetailService.deleteBicycleBillDetailByBillId(bicycleBill.getId());
        }
    }
    /**
     * 根据结算单id查询账单列表
     * @param settlementId 结算单id
     * @return 返回查询到的账单
     */
    public List<BicycleBill> billListBySettlementId(Long settlementId){
        if (!WrapperClassUtils.biggerThanLong(settlementId,0)){
            return Collections.emptyList();
        }
     return baseMapper.billListBySettlementId(settlementId);
    }

}
