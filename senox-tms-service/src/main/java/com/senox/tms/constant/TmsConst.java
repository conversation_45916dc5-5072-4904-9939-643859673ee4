package com.senox.tms.constant;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/9/13 13:54
 */
public class TmsConst {

    private TmsConst() {
    }


    /**
     * 缓存
     */
    public static class Cache{
        private Cache() {
        }

        /**
         * 三轮车配送地点
         */
        public static final String KEY_BICYCLE_POINT = "senox:bicycle:point";

        /**
         * 骑手编号
         */
        public static final String KEY_BICYCLE_RIDER_NO = "senox:bicycle:riderNo:%s";

        /**
         * 订单流水号
         */
        public static final String KEY_BICYCLE_ORDER_SERIAL = "senox:bicycle:orderSerial:%s";
        /**
         * 配送单流水号
         */
        public static final String KEY_BICYCLE_DELIVERY_ORDER_SERIAL = "senox:bicycleDelivery:orderSerial:%s";
        /**
         * 子配送单流水号
         */
        public static final String KEY_BICYCLE_DELIVERY_ORDER_ITEM_SERIAL = "senox:bicycleDeliveryItem:orderSerial:%s";

        /**
         * 日报表锁
         */
        public static final String KEY_BICYCLE_DAY_REPORT_LOCK = "senox:bicycle:report:day:lock:%s-%s-%s";
        /**
         * 月报表锁
         */
        public static final String KEY_BICYCLE_MONTH_REPORT_LOCK = "senox:bicycle:report:month:lock:%s-%s";

        /**
         * 应付日报表锁
         */
        public static final String KEY_BICYCLE_DYA_PAYOFF_BILL_REPORT_LOCK = "senox:bicycle:payoff:bill:report:day:lock:%s-%s-%s";

        /**
         * 应付月报表锁
         */
        public static final String KEY_BICYCLE_MONTH_PAYOFF_BILL_REPORT_LOCK = "senox:bicycle:payoff:bill:report:month:lock:%s-%s";

        public static final String KEY_BICYCLE_BILL_SETTLEMENT_LOCK = "senox:bicycle:bill:settlement:lock:%s";

        /**
         * 搬运工收益日报表锁
         */
        public static final String KEY_LOADER_INCOME_MONTH_REPORT_LOCK = "senox:loader:income:month:report:lock:%s-%s-%s";

        /**
         * 订单流水号
         */
        public static final String KEY_UNLOADING_ORDER_NO = "senox:unloading:orderNo:%s";

        /**
         * 搬运工标识
         */
        public static final String KEY_UNLOADING_WORKER_SIGN = "senox:unloading:workerSign:%s";

        /**
         * 缓存1h
         */
        public static final long TTL_1H = TimeUnit.HOURS.toSeconds(1L);

        /**
         * 缓存2天
         */
        public static final long TTL_2D = TimeUnit.DAYS.toSeconds(2);
        /**
         * 缓存7天
         */
        public static final long TTL_7D = TimeUnit.DAYS.toSeconds(7);
    }

    public static class MQ {
        private MQ() {
        }

        /**
         * 分配骑手消息
         */
        public static final String EX_TMS_AC_RIDER = "tms.ac.rider.exchange";
        public static final String MQ_TMS_AC_RIDER = "tms.ac.rider.queue";
        /**
         * 取消分配骑手消息
         */
        public static final String EX_TMS_AC_CANCEL_RIDER = "tms.ac.cancel.rider.exchange";
        public static final String MQ_TMS_AC_CANCEL_RIDER = "tms.ac.cancel.rider.queue";

        /**
         * 三轮车订单完成消息
         */
        public static final String EX_TMS_AC_ORDER_COMPLETE = "tms.ac.order.complete.exchange";
        public static final String MQ_TMS_AC_ORDER_COMPLETE = "tms.ac.order.complete.queue";

        /**
         * 三轮车骑手超时未拣货
         */
        public static final String EX_TMS_AC_TIMEOUT_UNPICKED_GOODS = "tms.ac.timeout.unpicked.goods.exchange";
        public static final String MQ_TMS_AC_TIMEOUT_UNPICKED_GOODS = "tms.ac.timeout.unpicked.goods.queue";

        /**
         * 三轮车订单未分配提醒
         */
        public static final String EX_TMS_AC_UN_ASSIGN = "tms.ac.un.assign.exchange";
        public static final String MQ_TMS_AC_UN_ASSIGN = "tms.ac.un.assign.queue";

        /**
         * 三轮车订单取消提醒
         */
        public static final String EX_TMS_AC_CANCEL_ORDER = "tms.ac.cancel.order.exchange";
        public static final String MQ_TMS_AC_CANCEL_ORDER = "tms.ac.cancel.order.queue";

        /**
         * 延时通知
         */
        public static final String EX_TMS_DELAYED = "tms.delayed.exchange";
        public static final String MQ_TMS_UNPICKED_GOODS = "tms.unpicked.goods.queue";
        public static final String KEY_TMS_UNPICKED_GOODS = "unpicked.goods";

        public static final String MQ_TMS_BICYCLE_BILL_SETTLEMENT_MESSAGE = "tms.bicycle.bill.settlement.message.queue";

        /**
         * 三轮车配送单状态更新通知
         */
        public static final String EX_TMS_AC_ORDER_STATUS_NOTIFY = "tms.ac.order.status.notify";
        public static final String MQ_TMS_AC_ORDER_STATUS_NOTIFY = "tms.ac.order.status.notify";

        /**
         * 骑手空闲自动接推荐商户单
         */
        public static final String EX_TMS_AC_AUTO_TAKING_ORDER = "tms.ac.auto.taking.order";
        public static final String MQ_TMS_AC_AUTO_TAKING_ORDER = "tms.ac.auto.taking.order";

        /**
         * 门禁设备权限授权
         */
        public static final String EX_DEVICE_AC_WORKER_RIGHT_AUTH = "device.ac.right.worker.auth.exchange";
        public static final String MQ_DEVICE_AC_WORKER_RIGHT_AUTH = "device.ac.right.worker.auth.queue";

        public static final String EX_DEVICE_AC_UW_WORKER = "device.ac.uw.worker.exchange";
        public static final String MQ_DEVICE_AC_UW_WORKER = "device.ac.uw.worker.queue";
    }

    public static final int BATCH_SIZE_1000 = 1000;
    public static final String TITLE_BICYCLE_BILL_TITLE = "%s-三轮车账单";
    public static final String TITLE_BICYCLE_BILL = "%s-%s三轮车账单";
}
