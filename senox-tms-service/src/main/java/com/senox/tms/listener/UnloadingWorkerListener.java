package com.senox.tms.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.rabbitmq.client.Channel;
import com.senox.common.utils.JsonUtils;
import com.senox.context.AdminContext;
import com.senox.context.PluginEnv;
import com.senox.dm.vo.EmployeeAccessStateVo;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.service.UnloadingWorkerAccessService;
import com.senox.tms.service.UnloadingWorkerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/5/30 13:46
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UnloadingWorkerListener {

    private final UnloadingWorkerAccessService workerAccessService;
    private final UnloadingWorkerService workerService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = TmsConst.MQ.MQ_DEVICE_AC_WORKER_RIGHT_AUTH, durable = "true", exclusive = "false", autoDelete = "false"),
            exchange = @Exchange(value = TmsConst.MQ.EX_DEVICE_AC_WORKER_RIGHT_AUTH, type = ExchangeTypes.TOPIC),
            key = TmsConst.MQ.MQ_DEVICE_AC_WORKER_RIGHT_AUTH
    ), containerFactory = "containerFactory")
    public void accessRightAuthListener(Message message, Channel channel) throws IOException {
        String mqMessage = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("收到门禁授权成功消息 {}", mqMessage);

        EmployeeAccessStateVo accessAuthState = JsonUtils.json2GenericObject(mqMessage, new TypeReference<EmployeeAccessStateVo>() {});
        workerAccessService.updateWorkerAccessState(accessAuthState);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = TmsConst.MQ.MQ_DEVICE_AC_UW_WORKER, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = TmsConst.MQ.EX_DEVICE_AC_UW_WORKER),
            key = TmsConst.MQ.MQ_DEVICE_AC_UW_WORKER
    ), containerFactory = "containerFactory")
    public void workerClockListener(Message message, Channel channel) throws IOException {
        String mqMessage = new String(message.getBody(), StandardCharsets.UTF_8);
        String workerSign = JsonUtils.json2GenericObject(mqMessage, new TypeReference<String>() {});
        log.info("收到搬运工扫脸消息 {}", workerSign);

        AdminContext.setPluginEnv(PluginEnv.builder().mq(true).build());
        workerService.updateWorkerStatus(workerSign, UnloadingWorkerStatus.ALREADY_LISTED.getNumber());
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }
}
