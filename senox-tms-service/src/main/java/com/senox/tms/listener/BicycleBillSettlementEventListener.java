package com.senox.tms.listener;

import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.tms.component.MerchantComponent;
import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.dto.BicycleBillSettlementContentAdditional;
import com.senox.tms.event.BicycleBillSettlementAdhocEvent;
import com.senox.tms.event.BicycleBillSettlementDailyEvent;
import com.senox.tms.event.BicycleBillSettlementEvent;
import com.senox.tms.event.BicycleGenerateBillPayoffEvent;
import com.senox.tms.service.BicycleBillService;
import com.senox.tms.service.BicycleBillSettlementService;
import com.senox.tms.service.BicycleOrderService;
import com.senox.tms.vo.BicycleBillVo;
import com.senox.user.constant.MerchantBillSettlePeriod;
import com.senox.user.vo.MerchantSearchVo;
import com.senox.user.vo.MerchantVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-11-8
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class BicycleBillSettlementEventListener {
    private final BicycleBillSettlementService billSettlementService;
    private final BicycleBillService billService;
    private final BicycleOrderService orderService;
    private final MerchantComponent merchantComponent;
    private final ApplicationEventPublisher publisher;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = BicycleBillSettlementAdhocEvent.class, fallbackExecution = true)
    public void onBicycleBillSettlementAdhoc(BicycleBillSettlementAdhocEvent event) {
        List<BicycleOrder> orders = event.getOrders();
        log.info("【Bicycle】Bill settlement ad-hoc. orders: {}", JsonUtils.object2Json(orders));
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        List<BicycleOrder> effectiveOrders = filterByOrderSettlePeriod(orders, event.getContentAdditional().getSettlePeriod());
        log.info("【Bicycle】Bill settlement ad-hoc. orders: {}", JsonUtils.object2Json(effectiveOrders));
        bicycleBillSettlementHandle(event.getContentAdditional(), effectiveOrders);
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = BicycleBillSettlementDailyEvent.class, fallbackExecution = true)
    public void onBicycleBillSettlementDaily(BicycleBillSettlementDailyEvent event) {
        List<BicycleBillVo> bills = event.getEventData();
        log.info("【Bicycle】Bill settlement daily. bills: {}", JsonUtils.object2Json(bills));
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        List<BicycleOrder> orders = orderService.findBySerialNoList(bills.stream().map(BicycleBillVo::getOrderSerialNo).collect(Collectors.toList()));
        List<BicycleOrder> effectiveOrders = filterByOrderSettlePeriod(orders, event.getContentAdditional().getSettlePeriod());
        log.info("【Bicycle】Bill settlement daily. orders: {}", JsonUtils.object2Json(effectiveOrders));
        bicycleBillSettlementHandle(event.getContentAdditional(), effectiveOrders);
    }

    /**
     * 账单结算处理
     * @param contentAdditional 附加参数
     * @param orders 订单集
     */
    public void bicycleBillSettlementHandle(BicycleBillSettlementContentAdditional contentAdditional, List<BicycleOrder> orders) {
        log.info("【Bicycle】Bill settlement. orders: {}", JsonUtils.object2Json(orders));
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        Map<String, BicycleOrder> orderMap = orders.stream().collect(Collectors.toMap(BicycleOrder::getOrderSerialNo, order -> order));
        List<BicycleBillVo> bills = billService.findByOrderSerialNos(new ArrayList<>(orderMap.keySet()));
        List<String> defectOrderSerialNos = new ArrayList<>();
        for (BicycleBillVo bill : bills) {
            BicycleOrder order = orderMap.get(bill.getOrderSerialNo());
            if (!DecimalUtils.equals(order.getTotalCharge(), bill.getAmount())) {
                log.info("【Bicycle】Bill amount not same, orderSerialNo is {}", order.getOrderSerialNo());
                defectOrderSerialNos.add(order.getOrderSerialNo());
                //订单最新金额覆盖到账单
                bill.setAmount(order.getTotalCharge());
            }
        }
        contentAdditional.setName(BicycleBillSettlementEvent.PROJECT_NAME);
        billSettlementService.saveByBill(bills, contentAdditional);
        if (!CollectionUtils.isEmpty(defectOrderSerialNos)) {
            //更新不对应的应收应付
            publisher.publishEvent(new BicycleGenerateBillPayoffEvent(this, defectOrderSerialNos));
        }
    }

    /**
     * 过滤订单结算周期
     * @param orders 订单集
     * @param settlePeriod 结算周期
     * @return 返回过滤后的订单集
     */
    private List<BicycleOrder> filterByOrderSettlePeriod(List<BicycleOrder> orders, MerchantBillSettlePeriod settlePeriod) {
        if (CollectionUtils.isEmpty(orders) || null == settlePeriod) {
            return Collections.emptyList();
        }
        MerchantSearchVo search = new MerchantSearchVo();
        search.setIds(orders.stream().map(BicycleOrder::getSenderId).distinct().collect(Collectors.toList()));
        List<Long> merchants = merchantComponent.list(search).stream().filter(m -> m.getSettlePeriod() == settlePeriod).map(MerchantVo::getId).collect(Collectors.toList());
        List<BicycleOrder> effectiveOrders = new ArrayList<>();
        for (BicycleOrder order : orders) {
            if (merchants.contains(order.getSenderId())) {
                effectiveOrders.add(order);
            }
        }
        return effectiveOrders;
    }
}
