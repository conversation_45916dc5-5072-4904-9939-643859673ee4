package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.LogisticsFreight;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
public interface LogisticsFreightMapper extends BaseMapper<LogisticsFreight> {

    /**
     * 列表查询
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    List<LogisticsFreight> list(LogisticsFreightSearchVo searchVo);

    /**
     * 列表统计查询
     *
     * @param searchVo 查询参数
     * @return 返回统计数
     */
    int countList(LogisticsFreightSearchVo searchVo);

    /**
     * 列表金额合计
     * @param searchVo 查询
     * @return 返回合计
     */
    LogisticsFreightStatisticsVo listTotalAmount(LogisticsFreightSearchVo searchVo);

    /**
     * 根据id查找
     * @param id id
     * @return 返回查找到的
     */
    LogisticsFreight findById(Long id);
}
