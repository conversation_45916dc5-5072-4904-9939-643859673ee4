package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingOrderPayoff;
import com.senox.tms.vo.UnloadingOrderPayoffSearchVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 10:20
 */
@Mapper
public interface UnloadingOrderPayoffMapper extends BaseMapper<UnloadingOrderPayoff> {

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    int countPayoff(UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 应付合计
     * @param searchVo
     * @return
     */
    UnloadingOrderPayoff sumPayoff(UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<UnloadingOrderPayoff> listPayoff(UnloadingOrderPayoffSearchVo searchVo);
}
