package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.vo.UnloadingWorkerSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/13 13:38
 */
@Mapper
public interface UnloadingWorkerMapper extends BaseMapper<UnloadingWorker> {

    /**
     * 获取最大的搬运工编号
     * @return
     */
    int getMaxOrderNum();

    /**
     * 根据序号筛选列表
     * @param orderNumStart
     * @param orderNumEnd
     * @return
     */
    List<UnloadingWorker> filterListWorker(@Param("orderNumStart") int orderNumStart, @Param("orderNumEnd") int orderNumEnd);

    /**
     * 获取最大搬运工标识
     * @param prefix
     * @return
     */
    String findMaxWorkerSign(String prefix);

    /**
     * 搬运工数量
     * @param searchVo
     * @return
     */
    int countWorker(UnloadingWorkerSearchVo searchVo);

    /**
     * 搬运工列表
     * @param searchVo
     * @return
     */
    List<UnloadingWorker> listWorker(UnloadingWorkerSearchVo searchVo);
}
