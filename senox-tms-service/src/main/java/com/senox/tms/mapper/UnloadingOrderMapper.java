package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingOrder;
import com.senox.tms.vo.UnloadOrderSearchVo;
import com.senox.tms.vo.UnloadingOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/14 11:17
 */
@Mapper
public interface UnloadingOrderMapper extends BaseMapper<UnloadingOrder> {


    /**
     * 获取最大订单编号
     * @param prefix
     * @return
     */
    String findMaxOrderNo(String prefix);

    /**
     * 订单数量
     * @param searchVo
     * @return
     */
    int countOrder(UnloadOrderSearchVo searchVo);

    /**
     * 订单列表
     * @param searchVo
     * @return
     */
    List<UnloadingOrderVo> listOrder(UnloadOrderSearchVo searchVo);


    /**
     * 订单合计
     * @param searchVo
     * @return
     */
    UnloadingOrderVo sumOrder(UnloadOrderSearchVo searchVo);

    /**
     * 根据id查询订单详细
     * @param id
     * @return
     */
    UnloadingOrderVo findDetailById(@Param("id") Long id);
}
