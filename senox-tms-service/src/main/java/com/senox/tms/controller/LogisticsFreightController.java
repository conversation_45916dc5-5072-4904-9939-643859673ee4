package com.senox.tms.controller;

import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.LogisticsFreightConvert;
import com.senox.tms.domain.LogisticsFreight;
import com.senox.tms.service.LogisticsFreightService;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;
import com.senox.tms.vo.LogisticsFreightVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
@Api(tags = "物流货运")
@RestController
@RequiredArgsConstructor
@RequestMapping("/logistics/freight")
public class LogisticsFreightController {
    private final LogisticsFreightService freightService;
    private final LogisticsFreightConvert freightConvert;

    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void add(@RequestBody List<LogisticsFreightVo> freightVos) {
        freightService.addBatch(freightConvert.toDo(freightVos));
    }

    @ApiOperation("根据id查找")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public LogisticsFreightVo findById(@PathVariable Long id) {
        return freightConvert.toV(freightService.findById(id));
    }

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody LogisticsFreightVo freightVo) {
        freightService.update(freightConvert.toDo(freightVo));
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        freightService.deleteById(id);
    }

    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<LogisticsFreightVo> list(@RequestBody LogisticsFreightSearchVo searchVo) {
        List<LogisticsFreight> list = freightService.list(searchVo);
        return list.stream().map(freightConvert::toV).collect(Collectors.toList());
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/page")
    public PageStatisticsResult<LogisticsFreightVo, LogisticsFreightStatisticsVo> listPage(@RequestBody LogisticsFreightSearchVo searchVo) {
        return PageStatisticsResult.convertPages(freightService.listPage(searchVo),freightConvert::toV);
    }

}
