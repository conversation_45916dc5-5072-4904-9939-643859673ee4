package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.UnloadingOrderPayoffConvert;
import com.senox.tms.service.UnloadingOrderPayoffService;
import com.senox.tms.vo.UnloadingMonthVo;
import com.senox.tms.vo.UnloadingOrderPayoffSearchVo;
import com.senox.tms.vo.UnloadingOrderPayoffVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 13:44
 */
@Api(tags = "鹏翔应付账单")
@RequiredArgsConstructor
@RequestMapping("/unloading/payoff")
@RestController
public class UnloadingOrderPayoffController {

    private final UnloadingOrderPayoffService payoffService;
    private final UnloadingOrderPayoffConvert payoffConvert;

    @ApiOperation("生成应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generateOrderPayoff(@RequestBody UnloadingMonthVo monthVo) {
        payoffService.generateOrderPayoff(monthVo);
    }

    @ApiOperation("根据id查询应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public UnloadingOrderPayoffVo findPayoffById(@PathVariable Long id) {
        return payoffConvert.toVo(payoffService.findPayoffById(id));
    }

    @ApiOperation("根据订单号删除应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/deleteByOrderNo")
    public void deletePayoffByOrderNo(@RequestParam String orderNo) {
        payoffService.deletePayoffByOrderNo(orderNo);
    }

    @ApiOperation("更新应付金额")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update/sharesAmount")
    public void updateSharesAmount(@RequestBody List<UnloadingOrderPayoffVo> payoffVoList) {
        payoffService.updateSharesAmount(payoffConvert.toDo(payoffVoList));
    }

    @ApiOperation("应付分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<UnloadingOrderPayoffVo> pagePayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageResult.convertPage(payoffService.pagePayoff(searchVo), payoffConvert::toVo);
    }

    @ApiOperation("应付合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sum")
    public UnloadingOrderPayoffVo sumPayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo) {
        return payoffConvert.toVo(payoffService.sumPayoff(searchVo));
    }

    @ApiOperation("批量更新应付状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batchPayoffByIds")
    public void batchPayoffByIds(@RequestBody List<Long> ids) {
        payoffService.batchPayoffByIds(ids);
    }

    @ApiOperation("更新应付状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batchPayoff")
    public void batchPayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo) {
        payoffService.batchPayoff(searchVo);
    }

    @ApiOperation("根据订单编号查询应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findByOrderNo")
    public List<UnloadingOrderPayoffVo> listPayoffByOrderNo(@RequestParam String orderNo) {
        return payoffConvert.toVo(payoffService.listPayoffByOrderNo(orderNo));
    }
}
