package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.UnloadingWorkerAccessConvert;
import com.senox.tms.convert.UnloadingWorkerConvert;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.domain.UnloadingWorkerAccess;
import com.senox.tms.service.UnloadingAttendanceService;
import com.senox.tms.service.UnloadingWorkerAccessService;
import com.senox.tms.service.UnloadingWorkerService;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26 10:57
 */
@Api(tags = "鹏翔搬运工")
@RequiredArgsConstructor
@RequestMapping("/unloading/worker")
@RestController
public class UnloadingWorkerController {

    private final UnloadingWorkerService workerService;
    private final UnloadingWorkerConvert workerConvert;
    private final UnloadingAttendanceService attendanceService;
    private final UnloadingWorkerAccessConvert workerAccessConvert;
    private final UnloadingWorkerAccessService workerAccessService;


    @ApiOperation("保存搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/save")
    public void saveWorker(@RequestBody UnloadingWorkerVo workerVo) {
        UnloadingWorker worker = workerConvert.toDo(workerVo);
        workerService.saveWorker(worker);
    }

    @ApiOperation("更新搬运工状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/updateStatus/{id}")
    public void updateWorkerStatus(@PathVariable Long id, @RequestParam Integer status) {
        workerService.updateWorkerStatus(id, status);
    }

    @ApiOperation("只更新人脸")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/updateFaceUrl")
    public void updateFaceUrl(@RequestBody UnloadingWorkerFaceUrlVo faceUrlVo) {
        workerService.updateFaceUrl(faceUrlVo);
    }

    @ApiOperation("重新排序")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/resetOrderNum/{id}")
    public void resetOrderNum(@PathVariable Long id, @RequestParam Long targetId) {
        workerService.resetOrderNum(id, targetId);
    }

    @ApiOperation("根据id查询搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public UnloadingWorkerVo findById(@PathVariable Long id) {
        return workerConvert.toVo(workerService.findById(id));
    }

    @ApiOperation("根据id逻辑删除搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteWorker(@PathVariable Long id) {
        workerService.deleteWorker(id);
    }

    @ApiOperation("搬运工列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<UnloadingWorkerVo> listWorker(@RequestBody UnloadingWorkerSearchVo searchVo) {
        return workerConvert.toVo(workerService.listWorker(searchVo));
    }

    @ApiOperation("搬运工分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<UnloadingWorkerVo> pageWorker(@RequestBody UnloadingWorkerSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageResult.convertPage(workerService.pageWorker(searchVo), workerConvert::toVo);
    }

    @ApiOperation("搬运工考勤记录分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/attendance/page")
    public PageResult<UnloadingAttendanceVo> pageAttendance(@RequestBody UnloadingAttendanceSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return attendanceService.pageAttendance(searchVo);
    }

    @ApiOperation("更新考勤记录备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/attendance/updateRemark/{id}")
    public void updateRemark(@PathVariable Long id, @RequestParam String remark) {
        attendanceService.updateRemark(id, remark);
    }

    @ApiOperation("添加搬运工设备权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/save")
    public void addWorkerAccess(@RequestBody List<UnloadingWorkerAccessVo> accessVoList) {
        List<UnloadingWorkerAccess> accessList = workerAccessConvert.toDo(accessVoList);
        Long workerId = accessList.stream().map(UnloadingWorkerAccess::getWorkerId).distinct().findFirst().orElse(0L);
        workerAccessService.addWorkerAccess(workerService.findById(workerId), accessList);
    }

    @ApiOperation("根据id删除搬运工设备权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/access/delete/{id}")
    public void deleteAccessById(@PathVariable Long id) {
        UnloadingWorkerAccess access = workerAccessService.findById(id);
        if (access == null) {
            return;
        }
        workerAccessService.deleteAccess(access, workerService.findById(access.getWorkerId()));
    }

    @ApiOperation("根据Id获取搬运工设备权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/get/{workerId}")
    public List<UnloadingWorkerAccessVo> listAccessByWorkerId(@PathVariable Long workerId) {
        return workerAccessConvert.toVo(workerAccessService.accessListByWorkerIds(Collections.singletonList(workerId)));
    }
}
