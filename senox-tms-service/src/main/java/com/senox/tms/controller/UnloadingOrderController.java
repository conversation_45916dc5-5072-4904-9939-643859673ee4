package com.senox.tms.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.UnloadingOrderConvert;
import com.senox.tms.convert.UnloadingOrderGoodsConvert;
import com.senox.tms.convert.UnloadingOrderWorkersConvert;
import com.senox.tms.domain.UnloadingOrder;
import com.senox.tms.domain.UnloadingOrderGoods;
import com.senox.tms.service.UnloadingOrderService;
import com.senox.tms.vo.UnloadOrderSearchVo;
import com.senox.tms.vo.UnloadingOrderGoodsVo;
import com.senox.tms.vo.UnloadingOrderVo;
import com.senox.tms.vo.UnloadingOrderWorkersVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26 15:34
 */
@Api(tags = "鹏翔搬运订单")
@RequiredArgsConstructor
@RequestMapping("/unloading/order")
@RestController
public class UnloadingOrderController {

    private final UnloadingOrderService orderService;
    private final UnloadingOrderConvert orderConvert;
    private final UnloadingOrderGoodsConvert orderGoodsConvert;
    private final UnloadingOrderWorkersConvert orderWorkersConvert;


    @ApiOperation("添加搬运订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void saveOrder(@RequestBody UnloadingOrderVo orderVo) {
        List<UnloadingOrderGoodsVo> goodsVoList = orderVo.getGoodsVoList();
        List<UnloadingOrderGoods> goodsList = orderGoodsConvert.toDo(goodsVoList);
        UnloadingOrder order = orderConvert.toDo(orderVo);
        orderService.saveOrder(order, goodsList);
    }

    @ApiOperation("删除搬运订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteOrder(@PathVariable Long id) {
        orderService.deleteOrder(id);
    }

    @ApiOperation("根据id搬运订单及详细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public UnloadingOrderVo findDetailById(@PathVariable Long id) {
        return orderService.findDetailById(id);
    }

    @ApiOperation("搬运订单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<UnloadingOrderVo> pageResult(@RequestBody UnloadOrderSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return orderService.pageResult(searchVo);
    }

    @ApiOperation("搬运订单合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sum")
    public UnloadingOrderVo sumOrder(@RequestBody UnloadOrderSearchVo searchVo) {
        return orderService.sumOrder(searchVo);
    }

    @ApiOperation("搬运订单详细分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/detail/page")
    public PageResult<UnloadingOrderVo> pageDetailResult(@RequestBody UnloadOrderSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return orderService.pageDetailResult(searchVo);
    }

    @ApiOperation("指定搬运工人数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/appointWorkerNum/{orderId}")
    public void appointWorkerNum(@PathVariable Long orderId, @RequestParam Integer workerNum) {
        orderService.appointWorkerNum(orderId, workerNum);
    }

    @ApiOperation("分派搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/assignWorkers")
    public void assignWorkers(@RequestBody UnloadingOrderVo orderVo) {
        if (!WrapperClassUtils.biggerThanLong(orderVo.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        List<UnloadingOrderWorkersVo> workersVoList = orderVo.getWorkersVoList();
        orderService.assignWorkers(orderVo.getId(), orderWorkersConvert.toDo(workersVoList));
    }

    @ApiOperation("完成订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/finishOrder/{orderId}")
    public void finishOrder(@PathVariable Long orderId, @RequestParam BigDecimal amount) {
        orderService.finishOrder(orderId, amount);
    }

    @ApiOperation("取消订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/cancelOrder/{orderId}")
    public void cancelOrder(@PathVariable Long orderId) {
        orderService.cancelOrder(orderId);
    }
}
