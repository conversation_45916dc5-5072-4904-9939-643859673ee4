package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.UnloadingOrderBillConvert;
import com.senox.tms.service.UnloadingOrderBillService;
import com.senox.tms.vo.UnloadingMonthVo;
import com.senox.tms.vo.UnloadingOrderBillSearchVo;
import com.senox.tms.vo.UnloadingOrderBillVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/1 9:37
 */
@Api(tags = "鹏翔应收账单")
@RequiredArgsConstructor
@RequestMapping("/unloading/bill")
@RestController
public class UnloadingOrderBillController {

    private final UnloadingOrderBillService orderBillService;
    private final UnloadingOrderBillConvert orderBillConvert;

    @ApiOperation("生成应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generateBill(@RequestBody UnloadingMonthVo monthVo) {
        orderBillService.generateBill(monthVo);
    }

    @ApiOperation("支付应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/pay/{id}")
    public void payUnloadingOrderBill(@PathVariable Long id) {
        orderBillService.payUnloadingOrderBill(id);
    }

    @ApiOperation("根据id查询应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public UnloadingOrderBillVo findBillById(@PathVariable Long id) {
        return orderBillConvert.toVo(orderBillService.findById(id));
    }

    @ApiOperation("根据id删除应收账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteBillById(@PathVariable Long id) {
        orderBillService.deleteById(id);
    }

    @ApiOperation("应收账单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<UnloadingOrderBillVo> pageBill(@RequestBody UnloadingOrderBillSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageResult.convertPage(orderBillService.pageOrderBill(searchVo), orderBillConvert::toVo);
    }

    @ApiOperation("应收账单合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sum")
    public UnloadingOrderBillVo sumBill(@RequestBody UnloadingOrderBillSearchVo searchVo) {
        return orderBillConvert.toVo(orderBillService.sumBill(searchVo));
    }

    @ApiOperation("更新订单应收金额")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/updateOrderBill/{orderNo}")
    public void updateOrderBill(@PathVariable String orderNo, @RequestParam BigDecimal amount) {
        orderBillService.updateOrderBill(orderNo, amount);
    }
}
