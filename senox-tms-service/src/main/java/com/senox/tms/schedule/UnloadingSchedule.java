package com.senox.tms.schedule;

import com.senox.context.AdminContext;
import com.senox.context.PluginEnv;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.service.UnloadingWorkerService;
import com.senox.tms.vo.UnloadingWorkerSearchVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/12/30 8:18
 */
@Component
@RequiredArgsConstructor
public class UnloadingSchedule {

    private final UnloadingWorkerService workerService;


    @XxlJob("executeOffWork")
    public void executeOffWork() {
        XxlJobHelper.log("搬运工下班重置状态开始...");
        long execStartTime = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());

        UnloadingWorkerSearchVo searchVo = new UnloadingWorkerSearchVo();
        searchVo.setPage(false);
        searchVo.setStatusList(Collections.singletonList(UnloadingWorkerStatus.ALREADY_LISTED.getNumber()));
        List<UnloadingWorker> workers =
                workerService.listWorker(searchVo);
        if (!CollectionUtils.isEmpty(workers)) {
            workerService.updateBatchWorkerStatus(workers.stream().map(UnloadingWorker::getId).collect(Collectors.toList()), UnloadingWorkerStatus.NOT_LISTED.getNumber());
        }

        XxlJobHelper.log("搬运工下班重置状态结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

}
