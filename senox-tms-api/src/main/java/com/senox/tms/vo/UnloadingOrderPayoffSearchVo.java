package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 9:07
 */
@ApiModel("鹏翔应付账单查询参数")
@Getter
@Setter
public class UnloadingOrderPayoffSearchVo extends PageRequest {
    private static final long serialVersionUID = -5455194703299036825L;


    /**
     * 应付日期起
     */
    @ApiModelProperty("应付日期起")
    private LocalDate payoffDateStart;

    /**
     * 应付日期止
     */
    @ApiModelProperty("应付日期止")
    private LocalDate payoffDateEnd;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;

    /**
     * 应付状态
     * @see UnloadingOrderBillPayoffStatus
     */
    @ApiModelProperty("应付状态 0初始化 1已支付")
    private Integer status;

    /**
     * 应付时间起
     */
    @ApiModelProperty("应付时间起")
    private LocalDateTime payoffTimeStart;

    /**
     * 应付时间止
     */
    @ApiModelProperty("应付时间止")
    private LocalDateTime payoffTimeEnd;

    /**
     * 订单号集合
     */
    @ApiModelProperty("订单号集合")
    private List<String> orderNoList;
}
