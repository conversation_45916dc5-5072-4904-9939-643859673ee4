package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
public enum LogisticLoaderGoodsType {

    /**
     * 干调
     */
    DRY_SPICES(1, "干调"),

    /**
     * 重货
     */
    HEAVY_CARGO(2, "重货"),

    /**
     * 贴箱
     */
    BOX_PACKING(3, "贴箱"),

    /**
     * 印章
     */
    YIN_ZHANG(4, "印章"),

    /**
     * 分拣
     */
    SORTING(5, "分拣"),

    /**
     * 扫码出库
     */
    SCAN_CODE_OUT_WAREHOUSE(6, "扫码出库"),
    ;

    private final int number;
    private final String name;

    LogisticLoaderGoodsType(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static LogisticLoaderGoodsType fromNumber(Integer number) {
        if (null == number) {
            return null;
        }
        for (LogisticLoaderGoodsType item : values()) {
            if (number.equals(item.number)) {
                return item;
            }
        }
        return null;
    }
}
