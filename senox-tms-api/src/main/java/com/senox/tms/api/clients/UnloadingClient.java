package com.senox.tms.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.api.UnloadingServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11 8:27
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface UnloadingClient {

    /**
     * 批量添加字典
     * @param dictVos
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_BATCH_ADD)
    void batchAddDict(@RequestBody List<UnloadingDictVo> dictVos);

    /**
     * 添加字典
     * @param dictVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_ADD)
    void addDict(@RequestBody UnloadingDictVo dictVo);

    /**
     * 更新字典
     * @param dictVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_UPDATE)
    void updateDict(@RequestBody UnloadingDictVo dictVo);

    /**
     * 根据id查询字典
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_DICT_GET)
    UnloadingDictVo findById(@PathVariable Long id);

    /**
     * 根据id删除字典
     * @param id
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_DELETE)
    void deleteDict(@PathVariable Long id);

    /**
     * 字典分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_PAGE)
    PageResult<UnloadingDictVo> dictPageResult(@RequestBody UnloadingDictSearchVo searchVo);

    /**
     * 保存搬运工
     * @param workerVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_SAVE)
    void saveWorker(@RequestBody UnloadingWorkerVo workerVo);

    /**
     * 更新搬运工状态
     * @param id
     * @param status
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_UPDATE_STATUS)
    void updateWorkerStatus(@PathVariable Long id, @RequestParam Integer status);

    /**
     * 只更新人脸
     * @param faceUrlVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_UPDATE_FACE_URL)
    void updateFaceUrl(@RequestBody UnloadingWorkerFaceUrlVo faceUrlVo);

    /**
     * 重新排序
     * @param id
     * @param targetId
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_RESET_ORDER_NUM)
    void resetOrderNum(@PathVariable Long id, @RequestParam Long targetId);

    /**
     * 根据id查询搬运工
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_GET)
    UnloadingWorkerVo findWorkById(@PathVariable Long id);

    /**
     * 根据id删除搬运工
     * @param id
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_DELETE)
    void deleteWorker(@PathVariable Long id);

    /**
     * 搬运工列表
      * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_LIST)
    List<UnloadingWorkerVo> listWorker(@RequestBody UnloadingWorkerSearchVo searchVo);

    /**
     * 搬运工分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_PAGE)
    PageResult<UnloadingWorkerVo> pageWorker(@RequestBody UnloadingWorkerSearchVo searchVo);

    /**
     * 搬运工考勤记录分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_ATTENDANCE_PAGE)
    PageResult<UnloadingAttendanceVo> pageAttendance(@RequestBody UnloadingAttendanceSearchVo searchVo);

    /**
     * 更新考勤记录备注
     * @param id
     * @param remark
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_ATTENDANCE_UPDATE_REMARK)
    void updateRemark(@PathVariable Long id, @RequestParam String remark);

    /**
     * 添加搬运工设备权限
     * @param accessVoList
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_ACCESS_SAVE)
    void addWorkerAccess(@RequestBody List<UnloadingWorkerAccessVo> accessVoList);

    /**
     * 根据id删除搬运工设备权限
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_ACCESS_DELETE)
    void deleteAccessById(@PathVariable Long id);

    /**
     * 根据Id获取搬运工设备权限
     * @param workerId
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_ACCESS_GET)
    List<UnloadingWorkerAccessVo> listAccessByWorkerId(@PathVariable Long workerId);

    /**
     * 添加搬运订单
     * @param orderVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_ADD)
    void saveOrder(@RequestBody UnloadingOrderVo orderVo);

    /**
     * 删除搬运订单
     * @param id
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_DELETE)
    void deleteOrder(@PathVariable Long id);

    /**
     * 根据id搬运订单及详细
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_ORDER_GET)
    UnloadingOrderVo findDetailById(@PathVariable Long id);

    /**
     * 搬运订单分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_PAGE)
    PageResult<UnloadingOrderVo> pageResult(@RequestBody UnloadOrderSearchVo searchVo);

    /**
     * 搬运订单合计
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_SUM)
    UnloadingOrderVo sumOrder(@RequestBody UnloadOrderSearchVo searchVo);

    /**
     * 搬运订单详细分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_DETAIL_PAGE)
    PageResult<UnloadingOrderVo> pageDetailResult(@RequestBody UnloadOrderSearchVo searchVo);

    /**
     * 指定搬运工人数
     * @param orderId
     * @param workerNum
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_ORDER_APPOINT_WORKER_NUM)
    void appointWorkerNum(@PathVariable Long orderId, @RequestParam Integer workerNum);

    /**
     * 分派搬运工
     * @param orderVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_ASSIGN_WORKERS)
    void assignWorkers(@RequestBody UnloadingOrderVo orderVo);

    /**
     * 完成订单
     * @param orderId
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_ORDER_FINISH_ORDER)
    void finishOrder(@PathVariable Long orderId, @RequestParam BigDecimal amount);

    /**
     * 取消订单
     * @param orderId
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_ORDER_CANCEL_ORDER)
    void cancelOrder(@PathVariable Long orderId);

    /**
     * 添加分佣
     * @param sharesVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_SHARES_SAVE)
    void saveShares(@RequestBody UnloadingSharesVo sharesVo);

    /**
     * 根据id获取分佣
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_SHARES_GET)
    UnloadingSharesVo findSharesById(@PathVariable Long id);

    /**
     * 根据id删除分佣
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_SHARES_DELETE)
    void deleteSharesById(@PathVariable Long id);

    /**
     * 分佣分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_SHARES_PAGE)
    PageResult<UnloadingSharesVo> pageShares(@RequestBody UnloadingSharesSearchVo searchVo);

    /**
     * 生成应收账单
     * @param monthVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_BILL_GENERATE)
    void generateBill(@RequestBody UnloadingMonthVo monthVo);

    /**
     * 支付应收账单
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_BILL_PAY)
    void payUnloadingOrderBill(@PathVariable Long id);

    /**
     * 根据id查询应收账单
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_BILL_GET)
    UnloadingOrderBillVo findBillById(@PathVariable Long id);

    /**
     * 根据id删除应收账单
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_BILL_DELETE)
    void deleteBillById(@PathVariable Long id);

    /**
     * 应收账单分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_BILL_PAGE)
    PageResult<UnloadingOrderBillVo> pageBill(@RequestBody UnloadingOrderBillSearchVo searchVo);

    /**
     * 应收账单合计
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_BILL_SUM)
    UnloadingOrderBillVo sumBill(@RequestBody UnloadingOrderBillSearchVo searchVo);

    /**
     * 更新订单应收金额
     * @param orderNo
     * @param amount
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_BILL_UPDATE_ORDER_BILL)
    void updateOrderBill(@PathVariable String orderNo, @RequestParam BigDecimal amount);

    /**
     * 生成应付账单
     * @param monthVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_GENERATE)
    void generateOrderPayoff(@RequestBody UnloadingMonthVo monthVo);

    /**
     * 根据id查询应付记录
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_GET)
    UnloadingOrderPayoffVo findPayoffById(@PathVariable Long id);

    /**
     * 根据订单号删除应付记录
     * @param orderNo
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_DELETE)
    void deletePayoffByOrderNo(@RequestParam String orderNo);

    /**
     * 更新应付金额
     * @param payoffVoList
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_UPDATE_SHARES_AMOUNT)
    void updateSharesAmount(@RequestBody List<UnloadingOrderPayoffVo> payoffVoList);

    /**
     * 应付分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_PAGE)
    PageResult<UnloadingOrderPayoffVo> pagePayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 应付合计
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_SUM)
    UnloadingOrderPayoffVo sumPayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 批量更新应付状态
     * @param ids
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_BATCH_PAYOFF_BY_IDS)
    void batchPayoffByIds(@RequestBody List<Long> ids);

    /**
     * 更新应付状态
     * @param searchVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_BATCH_PAYOFF)
    void batchPayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 根据订单编号查询应付记录
     * @param orderNo
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_FIND_BY_ORDER_NO)
    List<UnloadingOrderPayoffVo> listPayoffByOrderNo(@RequestParam String orderNo);
}
